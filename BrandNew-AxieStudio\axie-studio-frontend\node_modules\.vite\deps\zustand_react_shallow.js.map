{"version": 3, "sources": ["../../zustand/esm/react/shallow.mjs"], "sourcesContent": ["import ReactExports from 'react';\n\nfunction shallow(objA, objB) {\n  if (Object.is(objA, objB)) {\n    return true;\n  }\n  if (typeof objA !== \"object\" || objA === null || typeof objB !== \"object\" || objB === null) {\n    return false;\n  }\n  if (objA instanceof Map && objB instanceof Map) {\n    if (objA.size !== objB.size) return false;\n    for (const [key, value] of objA) {\n      if (!Object.is(value, objB.get(key))) {\n        return false;\n      }\n    }\n    return true;\n  }\n  if (objA instanceof Set && objB instanceof Set) {\n    if (objA.size !== objB.size) return false;\n    for (const value of objA) {\n      if (!objB.has(value)) {\n        return false;\n      }\n    }\n    return true;\n  }\n  const keysA = Object.keys(objA);\n  if (keysA.length !== Object.keys(objB).length) {\n    return false;\n  }\n  for (const keyA of keysA) {\n    if (!Object.prototype.hasOwnProperty.call(objB, keyA) || !Object.is(objA[keyA], objB[keyA])) {\n      return false;\n    }\n  }\n  return true;\n}\n\nconst { useRef } = ReactExports;\nfunction useShallow(selector) {\n  const prev = useRef();\n  return (state) => {\n    const next = selector(state);\n    return shallow(prev.current, next) ? prev.current : prev.current = next;\n  };\n}\n\nexport { useShallow };\n"], "mappings": ";;;;;;;;AAAA,mBAAyB;AAEzB,SAAS,QAAQ,MAAM,MAAM;AAC3B,MAAI,OAAO,GAAG,MAAM,IAAI,GAAG;AACzB,WAAO;AAAA,EACT;AACA,MAAI,OAAO,SAAS,YAAY,SAAS,QAAQ,OAAO,SAAS,YAAY,SAAS,MAAM;AAC1F,WAAO;AAAA,EACT;AACA,MAAI,gBAAgB,OAAO,gBAAgB,KAAK;AAC9C,QAAI,KAAK,SAAS,KAAK,KAAM,QAAO;AACpC,eAAW,CAAC,KAAK,KAAK,KAAK,MAAM;AAC/B,UAAI,CAAC,OAAO,GAAG,OAAO,KAAK,IAAI,GAAG,CAAC,GAAG;AACpC,eAAO;AAAA,MACT;AAAA,IACF;AACA,WAAO;AAAA,EACT;AACA,MAAI,gBAAgB,OAAO,gBAAgB,KAAK;AAC9C,QAAI,KAAK,SAAS,KAAK,KAAM,QAAO;AACpC,eAAW,SAAS,MAAM;AACxB,UAAI,CAAC,KAAK,IAAI,KAAK,GAAG;AACpB,eAAO;AAAA,MACT;AAAA,IACF;AACA,WAAO;AAAA,EACT;AACA,QAAM,QAAQ,OAAO,KAAK,IAAI;AAC9B,MAAI,MAAM,WAAW,OAAO,KAAK,IAAI,EAAE,QAAQ;AAC7C,WAAO;AAAA,EACT;AACA,aAAW,QAAQ,OAAO;AACxB,QAAI,CAAC,OAAO,UAAU,eAAe,KAAK,MAAM,IAAI,KAAK,CAAC,OAAO,GAAG,KAAK,IAAI,GAAG,KAAK,IAAI,CAAC,GAAG;AAC3F,aAAO;AAAA,IACT;AAAA,EACF;AACA,SAAO;AACT;AAEA,IAAM,EAAE,OAAO,IAAI,aAAAA;AACnB,SAAS,WAAW,UAAU;AAC5B,QAAM,OAAO,OAAO;AACpB,SAAO,CAAC,UAAU;AAChB,UAAM,OAAO,SAAS,KAAK;AAC3B,WAAO,QAAQ,KAAK,SAAS,IAAI,IAAI,KAAK,UAAU,KAAK,UAAU;AAAA,EACrE;AACF;", "names": ["ReactExports"]}