{"name": "ag-charts-types", "version": "10.3.8", "description": "Advanced Charting / Charts supporting Javascript / Typescript / React / Angular / Vue", "main": "./dist/package/main.cjs.js", "types": "./dist/types/src/main.d.ts", "module": "./dist/package/main.esm.mjs", "exports": {"import": "./dist/package/main.esm.mjs", "require": "./dist/package/main.cjs.js", "types": "./dist/types/src/main.d.ts", "default": "./dist/package/main.cjs.js"}, "scripts": {"lint:prune": "npx ts-prune -p tsconfig.lib.json"}, "repository": {"type": "git", "url": "https://github.com/ag-grid/ag-charts.git"}, "keywords": ["chart", "charts", "data", "angular", "angular-component", "react", "react-component", "reactjs", "vue", "v<PERSON><PERSON><PERSON>"], "author": "AG Grid <<EMAIL>>", "license": "MIT", "bugs": {"url": "https://github.com/ag-grid/ag-charts/issues"}, "browserslist": ["> 1%", "last 2 versions", "not ie >= 0", "not ie_mob >= 0", "not blackberry > 0", "not op_mini all", "not operamobile >= 0"], "homepage": "https://ag-grid.com/charts/", "dependencies": {}, "devDependencies": {}, "publishConfig": {"access": "public"}}