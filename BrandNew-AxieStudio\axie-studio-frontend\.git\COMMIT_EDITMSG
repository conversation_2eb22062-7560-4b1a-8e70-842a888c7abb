fix: Fix Vercel SPA routing to prevent 404 errors on refresh

- Enhanced vercel.json with explicit route mappings for all SPA routes
- Added _redirects file as fallback for client-side routing
- Explicitly defined routes for /flows, /settings, /login, /signup, /admin, etc.
- Added cleanUrls and trailingSlash configuration for better routing
- All routes now properly serve index.html for client-side routing
- Fixes 404 errors when users refresh on any page
