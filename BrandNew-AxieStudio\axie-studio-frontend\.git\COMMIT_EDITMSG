feat: Improve routing and authentication persistence

- Enhanced auth guard to preserve user's intended destination with proper URL encoding
- Added redirect handling after successful login to return users to their original page
- Improved Vercel configuration for better SPA routing support
- Enhanced auth store with session validation for 24-hour persistence
- Fixed catch-all route to redirect to /flows instead of /
- Better handling of login/signup page detection to prevent redirect loops
- Users can now refresh on any page (like /settings) and stay authenticated without 404s
