feat: Implement comprehensive user isolation system

- Add frontend user isolation utilities to prevent data leakage
- Implement multi-layer protection in messages store and API queries
- Add user-specific filtering for messages display
- Create development debugging tools for testing isolation
- Fix infinite recursion bug in removeMessages function
- Ensure backward compatibility with existing message structure
- Add TypeScript types for user_id field in messages

This addresses critical security issue where users could see
messages from other users due to backend not filtering by user.
