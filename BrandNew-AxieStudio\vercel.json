{"buildCommand": "cd axie-studio-frontend && npm run build", "outputDirectory": "axie-studio-frontend/dist", "installCommand": "cd axie-studio-frontend && npm install", "devCommand": "cd axie-studio-frontend && npm run dev", "rewrites": [{"source": "/api/(.*)", "destination": "https://langflow-tv34o.ondigitalocean.app/api/$1"}, {"source": "/((?!api).*)", "destination": "/index.html"}], "headers": [{"source": "/(.*)", "headers": [{"key": "X-Content-Type-Options", "value": "nosniff"}, {"key": "X-Frame-Options", "value": "DENY"}, {"key": "X-XSS-Protection", "value": "1; mode=block"}]}]}