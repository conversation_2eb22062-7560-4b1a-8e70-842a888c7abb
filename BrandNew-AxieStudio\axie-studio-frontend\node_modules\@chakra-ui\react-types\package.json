{"name": "@chakra-ui/react-types", "version": "2.0.7", "description": "", "keywords": ["react", "types", "shared"], "author": "<PERSON><PERSON> <<EMAIL>>", "homepage": "https://github.com/chakra-ui/chakra-ui#readme", "license": "MIT", "types": "src/index.d.ts", "sideEffects": false, "files": ["dist", "src"], "publishConfig": {"access": "public"}, "repository": {"type": "git", "url": "git+https://github.com/chakra-ui/chakra-ui.git", "directory": "packages/utilities/react-types"}, "bugs": {"url": "https://github.com/chakra-ui/chakra-ui/issues"}, "peerDependencies": {"react": ">=18"}, "devDependencies": {"react": "^18.0.0"}, "clean-package": "../../../clean-package.config.json", "tsup": {"clean": true, "target": "es2019", "format": ["cjs", "esm"]}}