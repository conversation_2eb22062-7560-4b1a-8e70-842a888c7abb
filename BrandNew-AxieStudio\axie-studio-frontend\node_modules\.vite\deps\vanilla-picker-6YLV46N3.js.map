{"version": 3, "sources": ["../../vanilla-picker/dist/vanilla-picker.mjs"], "sourcesContent": ["/*!\n * vanilla-picker v2.12.3\n * https://vanilla-picker.js.org\n *\n * Copyright 2017-2024 <PERSON> (https://github.com/Sphinxxxx), <PERSON> (https://github.com/dissimulate)\n * Released under the ISC license.\n */\nvar classCallCheck = function (instance, Constructor) {\n  if (!(instance instanceof Constructor)) {\n    throw new TypeError(\"Cannot call a class as a function\");\n  }\n};\n\nvar createClass = function () {\n  function defineProperties(target, props) {\n    for (var i = 0; i < props.length; i++) {\n      var descriptor = props[i];\n      descriptor.enumerable = descriptor.enumerable || false;\n      descriptor.configurable = true;\n      if (\"value\" in descriptor) descriptor.writable = true;\n      Object.defineProperty(target, descriptor.key, descriptor);\n    }\n  }\n\n  return function (Constructor, protoProps, staticProps) {\n    if (protoProps) defineProperties(Constructor.prototype, protoProps);\n    if (staticProps) defineProperties(Constructor, staticProps);\n    return Constructor;\n  };\n}();\n\nvar slicedToArray = function () {\n  function sliceIterator(arr, i) {\n    var _arr = [];\n    var _n = true;\n    var _d = false;\n    var _e = undefined;\n\n    try {\n      for (var _i = arr[Symbol.iterator](), _s; !(_n = (_s = _i.next()).done); _n = true) {\n        _arr.push(_s.value);\n\n        if (i && _arr.length === i) break;\n      }\n    } catch (err) {\n      _d = true;\n      _e = err;\n    } finally {\n      try {\n        if (!_n && _i[\"return\"]) _i[\"return\"]();\n      } finally {\n        if (_d) throw _e;\n      }\n    }\n\n    return _arr;\n  }\n\n  return function (arr, i) {\n    if (Array.isArray(arr)) {\n      return arr;\n    } else if (Symbol.iterator in Object(arr)) {\n      return sliceIterator(arr, i);\n    } else {\n      throw new TypeError(\"Invalid attempt to destructure non-iterable instance\");\n    }\n  };\n}();\n\nString.prototype.startsWith = String.prototype.startsWith || function (needle) {\n    return this.indexOf(needle) === 0;\n};\nString.prototype.padStart = String.prototype.padStart || function (len, pad) {\n    var str = this;while (str.length < len) {\n        str = pad + str;\n    }return str;\n};\n\nvar colorNames = { cb: '0f8ff', tqw: 'aebd7', q: '-ffff', qmrn: '7fffd4', zr: '0ffff', bg: '5f5dc', bsq: 'e4c4', bck: '---', nch: 'ebcd', b: '--ff', bvt: '8a2be2', brwn: 'a52a2a', brw: 'deb887', ctb: '5f9ea0', hrt: '7fff-', chcT: 'd2691e', cr: '7f50', rnw: '6495ed', crns: '8dc', crms: 'dc143c', cn: '-ffff', Db: '--8b', Dcn: '-8b8b', Dgnr: 'b8860b', Dgr: 'a9a9a9', Dgrn: '-64-', Dkhk: 'bdb76b', Dmgn: '8b-8b', Dvgr: '556b2f', Drng: '8c-', Drch: '9932cc', Dr: '8b--', Dsmn: 'e9967a', Dsgr: '8fbc8f', DsTb: '483d8b', DsTg: '2f4f4f', Dtrq: '-ced1', Dvt: '94-d3', ppnk: '1493', pskb: '-bfff', mgr: '696969', grb: '1e90ff', rbrc: 'b22222', rwht: 'af0', stg: '228b22', chs: '-ff', gnsb: 'dcdcdc', st: '8f8ff', g: 'd7-', gnr: 'daa520', gr: '808080', grn: '-8-0', grnw: 'adff2f', hnw: '0fff0', htpn: '69b4', nnr: 'cd5c5c', ng: '4b-82', vr: '0', khk: '0e68c', vnr: 'e6e6fa', nrb: '0f5', wngr: '7cfc-', mnch: 'acd', Lb: 'add8e6', Lcr: '08080', Lcn: 'e0ffff', Lgnr: 'afad2', Lgr: 'd3d3d3', Lgrn: '90ee90', Lpnk: 'b6c1', Lsmn: 'a07a', Lsgr: '20b2aa', Lskb: '87cefa', LsTg: '778899', Lstb: 'b0c4de', Lw: 'e0', m: '-ff-', mgrn: '32cd32', nn: 'af0e6', mgnt: '-ff', mrn: '8--0', mqm: '66cdaa', mmb: '--cd', mmrc: 'ba55d3', mmpr: '9370db', msg: '3cb371', mmsT: '7b68ee', '': '-fa9a', mtr: '48d1cc', mmvt: 'c71585', mnLb: '191970', ntc: '5fffa', mstr: 'e4e1', mccs: 'e4b5', vjw: 'dead', nv: '--80', c: 'df5e6', v: '808-0', vrb: '6b8e23', rng: 'a5-', rngr: '45-', rch: 'da70d6', pgnr: 'eee8aa', pgrn: '98fb98', ptrq: 'afeeee', pvtr: 'db7093', ppwh: 'efd5', pchp: 'dab9', pr: 'cd853f', pnk: 'c0cb', pm: 'dda0dd', pwrb: 'b0e0e6', prp: '8-080', cc: '663399', r: '--', sbr: 'bc8f8f', rb: '4169e1', sbrw: '8b4513', smn: 'a8072', nbr: '4a460', sgrn: '2e8b57', ssh: '5ee', snn: 'a0522d', svr: 'c0c0c0', skb: '87ceeb', sTb: '6a5acd', sTgr: '708090', snw: 'afa', n: '-ff7f', stb: '4682b4', tn: 'd2b48c', t: '-8080', thst: 'd8bfd8', tmT: '6347', trqs: '40e0d0', vt: 'ee82ee', whT: '5deb3', wht: '', hts: '5f5f5', w: '-', wgrn: '9acd32' };\n\nfunction printNum(num) {\n    var decs = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : 1;\n\n    var str = decs > 0 ? num.toFixed(decs).replace(/0+$/, '').replace(/\\.$/, '') : num.toString();\n    return str || '0';\n}\n\nvar Color = function () {\n    function Color(r, g, b, a) {\n        classCallCheck(this, Color);\n\n\n        var that = this;\n        function parseString(input) {\n\n            if (input.startsWith('hsl')) {\n                var _input$match$map = input.match(/([\\-\\d\\.e]+)/g).map(Number),\n                    _input$match$map2 = slicedToArray(_input$match$map, 4),\n                    h = _input$match$map2[0],\n                    s = _input$match$map2[1],\n                    l = _input$match$map2[2],\n                    _a = _input$match$map2[3];\n\n                if (_a === undefined) {\n                    _a = 1;\n                }\n\n                h /= 360;\n                s /= 100;\n                l /= 100;\n                that.hsla = [h, s, l, _a];\n            } else if (input.startsWith('rgb')) {\n                var _input$match$map3 = input.match(/([\\-\\d\\.e]+)/g).map(Number),\n                    _input$match$map4 = slicedToArray(_input$match$map3, 4),\n                    _r = _input$match$map4[0],\n                    _g = _input$match$map4[1],\n                    _b = _input$match$map4[2],\n                    _a2 = _input$match$map4[3];\n\n                if (_a2 === undefined) {\n                    _a2 = 1;\n                }\n\n                that.rgba = [_r, _g, _b, _a2];\n            } else {\n                if (input.startsWith('#')) {\n                    that.rgba = Color.hexToRgb(input);\n                } else {\n                    that.rgba = Color.nameToRgb(input) || Color.hexToRgb(input);\n                }\n            }\n        }\n\n        if (r === undefined) ; else if (Array.isArray(r)) {\n            this.rgba = r;\n        } else if (b === undefined) {\n            var color = r && '' + r;\n            if (color) {\n                parseString(color.toLowerCase());\n            }\n        } else {\n            this.rgba = [r, g, b, a === undefined ? 1 : a];\n        }\n    }\n\n    createClass(Color, [{\n        key: 'printRGB',\n        value: function printRGB(alpha) {\n            var rgb = alpha ? this.rgba : this.rgba.slice(0, 3),\n                vals = rgb.map(function (x, i) {\n                return printNum(x, i === 3 ? 3 : 0);\n            });\n\n            return alpha ? 'rgba(' + vals + ')' : 'rgb(' + vals + ')';\n        }\n    }, {\n        key: 'printHSL',\n        value: function printHSL(alpha) {\n            var mults = [360, 100, 100, 1],\n                suff = ['', '%', '%', ''];\n\n            var hsl = alpha ? this.hsla : this.hsla.slice(0, 3),\n                vals = hsl.map(function (x, i) {\n                return printNum(x * mults[i], i === 3 ? 3 : 1) + suff[i];\n            });\n\n            return alpha ? 'hsla(' + vals + ')' : 'hsl(' + vals + ')';\n        }\n    }, {\n        key: 'printHex',\n        value: function printHex(alpha) {\n            var hex = this.hex;\n            return alpha ? hex : hex.substring(0, 7);\n        }\n    }, {\n        key: 'rgba',\n        get: function get() {\n            if (this._rgba) {\n                return this._rgba;\n            }\n            if (!this._hsla) {\n                throw new Error('No color is set');\n            }\n\n            return this._rgba = Color.hslToRgb(this._hsla);\n        },\n        set: function set(rgb) {\n            if (rgb.length === 3) {\n                rgb[3] = 1;\n            }\n\n            this._rgba = rgb;\n            this._hsla = null;\n        }\n    }, {\n        key: 'rgbString',\n        get: function get() {\n            return this.printRGB();\n        }\n    }, {\n        key: 'rgbaString',\n        get: function get() {\n            return this.printRGB(true);\n        }\n    }, {\n        key: 'hsla',\n        get: function get() {\n            if (this._hsla) {\n                return this._hsla;\n            }\n            if (!this._rgba) {\n                throw new Error('No color is set');\n            }\n\n            return this._hsla = Color.rgbToHsl(this._rgba);\n        },\n        set: function set(hsl) {\n            if (hsl.length === 3) {\n                hsl[3] = 1;\n            }\n\n            this._hsla = hsl;\n            this._rgba = null;\n        }\n    }, {\n        key: 'hslString',\n        get: function get() {\n            return this.printHSL();\n        }\n    }, {\n        key: 'hslaString',\n        get: function get() {\n            return this.printHSL(true);\n        }\n    }, {\n        key: 'hex',\n        get: function get() {\n            var rgb = this.rgba,\n                hex = rgb.map(function (x, i) {\n                return i < 3 ? x.toString(16) : Math.round(x * 255).toString(16);\n            });\n\n            return '#' + hex.map(function (x) {\n                return x.padStart(2, '0');\n            }).join('');\n        },\n        set: function set(hex) {\n            this.rgba = Color.hexToRgb(hex);\n        }\n    }], [{\n        key: 'hexToRgb',\n        value: function hexToRgb(input) {\n\n            var hex = (input.startsWith('#') ? input.slice(1) : input).replace(/^(\\w{3})$/, '$1F').replace(/^(\\w)(\\w)(\\w)(\\w)$/, '$1$1$2$2$3$3$4$4').replace(/^(\\w{6})$/, '$1FF');\n\n            if (!hex.match(/^([0-9a-fA-F]{8})$/)) {\n                throw new Error('Unknown hex color; ' + input);\n            }\n\n            var rgba = hex.match(/^(\\w\\w)(\\w\\w)(\\w\\w)(\\w\\w)$/).slice(1).map(function (x) {\n                return parseInt(x, 16);\n            });\n\n            rgba[3] = rgba[3] / 255;\n            return rgba;\n        }\n    }, {\n        key: 'nameToRgb',\n        value: function nameToRgb(input) {\n\n            var hash = input.toLowerCase().replace('at', 'T').replace(/[aeiouyldf]/g, '').replace('ght', 'L').replace('rk', 'D').slice(-5, 4),\n                hex = colorNames[hash];\n            return hex === undefined ? hex : Color.hexToRgb(hex.replace(/\\-/g, '00').padStart(6, 'f'));\n        }\n    }, {\n        key: 'rgbToHsl',\n        value: function rgbToHsl(_ref) {\n            var _ref2 = slicedToArray(_ref, 4),\n                r = _ref2[0],\n                g = _ref2[1],\n                b = _ref2[2],\n                a = _ref2[3];\n\n            r /= 255;\n            g /= 255;\n            b /= 255;\n\n            var max = Math.max(r, g, b),\n                min = Math.min(r, g, b);\n            var h = void 0,\n                s = void 0,\n                l = (max + min) / 2;\n\n            if (max === min) {\n                h = s = 0;\n            } else {\n                var d = max - min;\n                s = l > 0.5 ? d / (2 - max - min) : d / (max + min);\n                switch (max) {\n                    case r:\n                        h = (g - b) / d + (g < b ? 6 : 0);break;\n                    case g:\n                        h = (b - r) / d + 2;break;\n                    case b:\n                        h = (r - g) / d + 4;break;\n                }\n\n                h /= 6;\n            }\n\n            return [h, s, l, a];\n        }\n    }, {\n        key: 'hslToRgb',\n        value: function hslToRgb(_ref3) {\n            var _ref4 = slicedToArray(_ref3, 4),\n                h = _ref4[0],\n                s = _ref4[1],\n                l = _ref4[2],\n                a = _ref4[3];\n\n            var r = void 0,\n                g = void 0,\n                b = void 0;\n\n            if (s === 0) {\n                r = g = b = l;\n            } else {\n                var hue2rgb = function hue2rgb(p, q, t) {\n                    if (t < 0) t += 1;\n                    if (t > 1) t -= 1;\n                    if (t < 1 / 6) return p + (q - p) * 6 * t;\n                    if (t < 1 / 2) return q;\n                    if (t < 2 / 3) return p + (q - p) * (2 / 3 - t) * 6;\n                    return p;\n                };\n\n                var q = l < 0.5 ? l * (1 + s) : l + s - l * s,\n                    p = 2 * l - q;\n\n                r = hue2rgb(p, q, h + 1 / 3);\n                g = hue2rgb(p, q, h);\n                b = hue2rgb(p, q, h - 1 / 3);\n            }\n\n            var rgba = [r * 255, g * 255, b * 255].map(Math.round);\n            rgba[3] = a;\n\n            return rgba;\n        }\n    }]);\n    return Color;\n}();\n\nvar EventBucket = function () {\n    function EventBucket() {\n        classCallCheck(this, EventBucket);\n\n        this._events = [];\n    }\n\n    createClass(EventBucket, [{\n        key: 'add',\n        value: function add(target, type, handler) {\n            target.addEventListener(type, handler, false);\n            this._events.push({\n                target: target,\n                type: type,\n                handler: handler\n            });\n        }\n    }, {\n        key: 'remove',\n        value: function remove(target, type, handler) {\n            this._events = this._events.filter(function (e) {\n                var isMatch = true;\n                if (target && target !== e.target) {\n                    isMatch = false;\n                }\n                if (type && type !== e.type) {\n                    isMatch = false;\n                }\n                if (handler && handler !== e.handler) {\n                    isMatch = false;\n                }\n\n                if (isMatch) {\n                    EventBucket._doRemove(e.target, e.type, e.handler);\n                }\n                return !isMatch;\n            });\n        }\n    }, {\n        key: 'destroy',\n        value: function destroy() {\n            this._events.forEach(function (e) {\n                return EventBucket._doRemove(e.target, e.type, e.handler);\n            });\n            this._events = [];\n        }\n    }], [{\n        key: '_doRemove',\n        value: function _doRemove(target, type, handler) {\n            target.removeEventListener(type, handler, false);\n        }\n    }]);\n    return EventBucket;\n}();\n\nfunction parseHTML(htmlString) {\n\n    var div = document.createElement('div');\n    div.innerHTML = htmlString;\n    return div.firstElementChild;\n}\n\nfunction dragTrack(eventBucket, area, callback) {\n    var dragging = false;\n\n    function clamp(val, min, max) {\n        return Math.max(min, Math.min(val, max));\n    }\n\n    function onMove(e, info, starting) {\n        if (starting) {\n            dragging = true;\n        }\n        if (!dragging) {\n            return;\n        }\n\n        e.preventDefault();\n\n        var bounds = area.getBoundingClientRect(),\n            w = bounds.width,\n            h = bounds.height,\n            x = info.clientX,\n            y = info.clientY;\n\n        var relX = clamp(x - bounds.left, 0, w),\n            relY = clamp(y - bounds.top, 0, h);\n\n        callback(relX / w, relY / h);\n    }\n\n    function onMouse(e, starting) {\n        var button = e.buttons === undefined ? e.which : e.buttons;\n        if (button === 1) {\n            onMove(e, e, starting);\n        } else {\n            dragging = false;\n        }\n    }\n\n    function onTouch(e, starting) {\n        if (e.touches.length === 1) {\n            onMove(e, e.touches[0], starting);\n        } else {\n            dragging = false;\n        }\n    }\n\n    eventBucket.add(area, 'mousedown', function (e) {\n        onMouse(e, true);\n    });\n    eventBucket.add(area, 'touchstart', function (e) {\n        onTouch(e, true);\n    });\n    eventBucket.add(window, 'mousemove', onMouse);\n    eventBucket.add(area, 'touchmove', onTouch);\n    eventBucket.add(window, 'mouseup', function (e) {\n        dragging = false;\n    });\n    eventBucket.add(area, 'touchend', function (e) {\n        dragging = false;\n    });\n    eventBucket.add(area, 'touchcancel', function (e) {\n        dragging = false;\n    });\n}\n\nvar BG_TRANSP = 'linear-gradient(45deg, lightgrey 25%, transparent 25%, transparent 75%, lightgrey 75%) 0 0 / 2em 2em,\\n                   linear-gradient(45deg, lightgrey 25%,       white 25%,       white 75%, lightgrey 75%) 1em 1em / 2em 2em';\nvar HUES = 360;\n\nvar EVENT_KEY = 'keydown',\n    EVENT_CLICK_OUTSIDE = 'mousedown',\n    EVENT_TAB_MOVE = 'focusin';\n\nfunction $(selector, context) {\n    return (context || document).querySelector(selector);\n}\n\nfunction stopEvent(e) {\n\n    e.preventDefault();\n    e.stopPropagation();\n}\nfunction onKey(bucket, target, keys, handler, stop) {\n    bucket.add(target, EVENT_KEY, function (e) {\n        if (keys.indexOf(e.key) >= 0) {\n            if (stop) {\n                stopEvent(e);\n            }\n            handler(e);\n        }\n    });\n}\n\nvar Picker = function () {\n    function Picker(options) {\n        classCallCheck(this, Picker);\n\n\n        this.settings = {\n\n            popup: 'right',\n            layout: 'default',\n            alpha: true,\n            editor: true,\n            editorFormat: 'hex',\n            cancelButton: false,\n            defaultColor: '#0cf'\n        };\n\n        this._events = new EventBucket();\n\n        this.onChange = null;\n\n        this.onDone = null;\n\n        this.onOpen = null;\n\n        this.onClose = null;\n\n        this.setOptions(options);\n    }\n\n    createClass(Picker, [{\n        key: 'setOptions',\n        value: function setOptions(options) {\n            var _this = this;\n\n            if (!options) {\n                return;\n            }\n            var settings = this.settings;\n\n            function transfer(source, target, skipKeys) {\n                for (var key in source) {\n                    if (skipKeys && skipKeys.indexOf(key) >= 0) {\n                        continue;\n                    }\n\n                    target[key] = source[key];\n                }\n            }\n\n            if (options instanceof HTMLElement) {\n                settings.parent = options;\n            } else {\n\n                if (settings.parent && options.parent && settings.parent !== options.parent) {\n                    this._events.remove(settings.parent);\n                    this._popupInited = false;\n                }\n\n                transfer(options, settings);\n\n                if (options.onChange) {\n                    this.onChange = options.onChange;\n                }\n                if (options.onDone) {\n                    this.onDone = options.onDone;\n                }\n                if (options.onOpen) {\n                    this.onOpen = options.onOpen;\n                }\n                if (options.onClose) {\n                    this.onClose = options.onClose;\n                }\n\n                var col = options.color || options.colour;\n                if (col) {\n                    this._setColor(col);\n                }\n            }\n\n            var parent = settings.parent;\n            if (parent && settings.popup && !this._popupInited) {\n\n                var openProxy = function openProxy(e) {\n                    return _this.openHandler(e);\n                };\n\n                this._events.add(parent, 'click', openProxy);\n\n                onKey(this._events, parent, [' ', 'Spacebar', 'Enter'], openProxy);\n\n                this._popupInited = true;\n            } else if (options.parent && !settings.popup) {\n                this.show();\n            }\n        }\n    }, {\n        key: 'openHandler',\n        value: function openHandler(e) {\n            if (this.show()) {\n\n                e && e.preventDefault();\n\n                this.settings.parent.style.pointerEvents = 'none';\n\n                var toFocus = e && e.type === EVENT_KEY ? this._domEdit : this.domElement;\n                setTimeout(function () {\n                    return toFocus.focus();\n                }, 100);\n\n                if (this.onOpen) {\n                    this.onOpen(this.colour);\n                }\n            }\n        }\n    }, {\n        key: 'closeHandler',\n        value: function closeHandler(e) {\n            var event = e && e.type;\n            var doHide = false;\n\n            if (!e) {\n                doHide = true;\n            } else if (event === EVENT_CLICK_OUTSIDE || event === EVENT_TAB_MOVE) {\n\n                var knownTime = (this.__containedEvent || 0) + 100;\n                if (e.timeStamp > knownTime) {\n                    doHide = true;\n                }\n            } else {\n\n                stopEvent(e);\n\n                doHide = true;\n            }\n\n            if (doHide && this.hide()) {\n                this.settings.parent.style.pointerEvents = '';\n\n                if (event !== EVENT_CLICK_OUTSIDE) {\n                    this.settings.parent.focus();\n                }\n\n                if (this.onClose) {\n                    this.onClose(this.colour);\n                }\n            }\n        }\n    }, {\n        key: 'movePopup',\n        value: function movePopup(options, open) {\n\n            this.closeHandler();\n\n            this.setOptions(options);\n            if (open) {\n                this.openHandler();\n            }\n        }\n    }, {\n        key: 'setColor',\n        value: function setColor(color, silent) {\n            this._setColor(color, { silent: silent });\n        }\n    }, {\n        key: '_setColor',\n        value: function _setColor(color, flags) {\n            if (typeof color === 'string') {\n                color = color.trim();\n            }\n            if (!color) {\n                return;\n            }\n\n            flags = flags || {};\n            var c = void 0;\n            try {\n\n                c = new Color(color);\n            } catch (ex) {\n                if (flags.failSilently) {\n                    return;\n                }\n                throw ex;\n            }\n\n            if (!this.settings.alpha) {\n                var hsla = c.hsla;\n                hsla[3] = 1;\n                c.hsla = hsla;\n            }\n            this.colour = this.color = c;\n            this._setHSLA(null, null, null, null, flags);\n        }\n    }, {\n        key: 'setColour',\n        value: function setColour(colour, silent) {\n            this.setColor(colour, silent);\n        }\n    }, {\n        key: 'show',\n        value: function show() {\n            var parent = this.settings.parent;\n            if (!parent) {\n                return false;\n            }\n\n            if (this.domElement) {\n                var toggled = this._toggleDOM(true);\n\n                this._setPosition();\n\n                return toggled;\n            }\n\n            var html = this.settings.template || '<div class=\"picker_wrapper\" tabindex=\"-1\"><div class=\"picker_arrow\"></div><div class=\"picker_hue picker_slider\"><div class=\"picker_selector\"></div></div><div class=\"picker_sl\"><div class=\"picker_selector\"></div></div><div class=\"picker_alpha picker_slider\"><div class=\"picker_selector\"></div></div><div class=\"picker_editor\"><input aria-label=\"Type a color name or hex value\"/></div><div class=\"picker_sample\"></div><div class=\"picker_done\"><button>Ok</button></div><div class=\"picker_cancel\"><button>Cancel</button></div></div>';\n            var wrapper = parseHTML(html);\n\n            this.domElement = wrapper;\n            this._domH = $('.picker_hue', wrapper);\n            this._domSL = $('.picker_sl', wrapper);\n            this._domA = $('.picker_alpha', wrapper);\n            this._domEdit = $('.picker_editor input', wrapper);\n            this._domSample = $('.picker_sample', wrapper);\n            this._domOkay = $('.picker_done button', wrapper);\n            this._domCancel = $('.picker_cancel button', wrapper);\n\n            wrapper.classList.add('layout_' + this.settings.layout);\n            if (!this.settings.alpha) {\n                wrapper.classList.add('no_alpha');\n            }\n            if (!this.settings.editor) {\n                wrapper.classList.add('no_editor');\n            }\n            if (!this.settings.cancelButton) {\n                wrapper.classList.add('no_cancel');\n            }\n            this._ifPopup(function () {\n                return wrapper.classList.add('popup');\n            });\n\n            this._setPosition();\n\n            if (this.colour) {\n                this._updateUI();\n            } else {\n                this._setColor(this.settings.defaultColor);\n            }\n            this._bindEvents();\n\n            return true;\n        }\n    }, {\n        key: 'hide',\n        value: function hide() {\n            return this._toggleDOM(false);\n        }\n    }, {\n        key: 'destroy',\n        value: function destroy() {\n            this._events.destroy();\n            if (this.domElement) {\n                this.settings.parent.removeChild(this.domElement);\n            }\n        }\n    }, {\n        key: '_bindEvents',\n        value: function _bindEvents() {\n            var _this2 = this;\n\n            var that = this,\n                dom = this.domElement,\n                events = this._events;\n\n            function addEvent(target, type, handler) {\n                events.add(target, type, handler);\n            }\n\n            addEvent(dom, 'click', function (e) {\n                return e.preventDefault();\n            });\n\n            dragTrack(events, this._domH, function (x, y) {\n                return that._setHSLA(x);\n            });\n\n            dragTrack(events, this._domSL, function (x, y) {\n                return that._setHSLA(null, x, 1 - y);\n            });\n\n            if (this.settings.alpha) {\n                dragTrack(events, this._domA, function (x, y) {\n                    return that._setHSLA(null, null, null, 1 - y);\n                });\n            }\n\n            var editInput = this._domEdit;\n            {\n                addEvent(editInput, 'input', function (e) {\n                    that._setColor(this.value, { fromEditor: true, failSilently: true });\n                });\n\n                addEvent(editInput, 'focus', function (e) {\n                    var input = this;\n\n                    if (input.selectionStart === input.selectionEnd) {\n                        input.select();\n                    }\n                });\n            }\n\n            this._ifPopup(function () {\n\n                var popupCloseProxy = function popupCloseProxy(e) {\n                    return _this2.closeHandler(e);\n                };\n\n                addEvent(window, EVENT_CLICK_OUTSIDE, popupCloseProxy);\n                addEvent(window, EVENT_TAB_MOVE, popupCloseProxy);\n                onKey(events, dom, ['Esc', 'Escape'], popupCloseProxy);\n\n                var timeKeeper = function timeKeeper(e) {\n                    _this2.__containedEvent = e.timeStamp;\n                };\n                addEvent(dom, EVENT_CLICK_OUTSIDE, timeKeeper);\n\n                addEvent(dom, EVENT_TAB_MOVE, timeKeeper);\n\n                addEvent(_this2._domCancel, 'click', popupCloseProxy);\n            });\n\n            var onDoneProxy = function onDoneProxy(e) {\n                _this2._ifPopup(function () {\n                    return _this2.closeHandler(e);\n                });\n                if (_this2.onDone) {\n                    _this2.onDone(_this2.colour);\n                }\n            };\n            addEvent(this._domOkay, 'click', onDoneProxy);\n            onKey(events, dom, ['Enter'], onDoneProxy);\n        }\n    }, {\n        key: '_setPosition',\n        value: function _setPosition() {\n            var parent = this.settings.parent,\n                elm = this.domElement;\n\n            if (parent !== elm.parentNode) {\n                parent.appendChild(elm);\n            }\n\n            this._ifPopup(function (popup) {\n\n                if (getComputedStyle(parent).position === 'static') {\n                    parent.style.position = 'relative';\n                }\n\n                var cssClass = popup === true ? 'popup_right' : 'popup_' + popup;\n\n                ['popup_top', 'popup_bottom', 'popup_left', 'popup_right'].forEach(function (c) {\n\n                    if (c === cssClass) {\n                        elm.classList.add(c);\n                    } else {\n                        elm.classList.remove(c);\n                    }\n                });\n\n                elm.classList.add(cssClass);\n            });\n        }\n    }, {\n        key: '_setHSLA',\n        value: function _setHSLA(h, s, l, a, flags) {\n            flags = flags || {};\n\n            var col = this.colour,\n                hsla = col.hsla;\n\n            [h, s, l, a].forEach(function (x, i) {\n                if (x || x === 0) {\n                    hsla[i] = x;\n                }\n            });\n            col.hsla = hsla;\n\n            this._updateUI(flags);\n\n            if (this.onChange && !flags.silent) {\n                this.onChange(col);\n            }\n        }\n    }, {\n        key: '_updateUI',\n        value: function _updateUI(flags) {\n            if (!this.domElement) {\n                return;\n            }\n            flags = flags || {};\n\n            var col = this.colour,\n                hsl = col.hsla,\n                cssHue = 'hsl(' + hsl[0] * HUES + ', 100%, 50%)',\n                cssHSL = col.hslString,\n                cssHSLA = col.hslaString;\n\n            var uiH = this._domH,\n                uiSL = this._domSL,\n                uiA = this._domA,\n                thumbH = $('.picker_selector', uiH),\n                thumbSL = $('.picker_selector', uiSL),\n                thumbA = $('.picker_selector', uiA);\n\n            function posX(parent, child, relX) {\n                child.style.left = relX * 100 + '%';\n            }\n            function posY(parent, child, relY) {\n                child.style.top = relY * 100 + '%';\n            }\n\n            posX(uiH, thumbH, hsl[0]);\n\n            this._domSL.style.backgroundColor = this._domH.style.color = cssHue;\n\n            posX(uiSL, thumbSL, hsl[1]);\n            posY(uiSL, thumbSL, 1 - hsl[2]);\n\n            uiSL.style.color = cssHSL;\n\n            posY(uiA, thumbA, 1 - hsl[3]);\n\n            var opaque = cssHSL,\n                transp = opaque.replace('hsl', 'hsla').replace(')', ', 0)'),\n                bg = 'linear-gradient(' + [opaque, transp] + ')';\n\n            this._domA.style.background = bg + ', ' + BG_TRANSP;\n\n            if (!flags.fromEditor) {\n                var format = this.settings.editorFormat,\n                    alpha = this.settings.alpha;\n\n                var value = void 0;\n                switch (format) {\n                    case 'rgb':\n                        value = col.printRGB(alpha);break;\n                    case 'hsl':\n                        value = col.printHSL(alpha);break;\n                    default:\n                        value = col.printHex(alpha);\n                }\n                this._domEdit.value = value;\n            }\n\n            this._domSample.style.color = cssHSLA;\n        }\n    }, {\n        key: '_ifPopup',\n        value: function _ifPopup(actionIf, actionElse) {\n            if (this.settings.parent && this.settings.popup) {\n                actionIf && actionIf(this.settings.popup);\n            } else {\n                actionElse && actionElse();\n            }\n        }\n    }, {\n        key: '_toggleDOM',\n        value: function _toggleDOM(toVisible) {\n            var dom = this.domElement;\n            if (!dom) {\n                return false;\n            }\n\n            var displayStyle = toVisible ? '' : 'none',\n                toggle = dom.style.display !== displayStyle;\n\n            if (toggle) {\n                dom.style.display = displayStyle;\n            }\n            return toggle;\n        }\n    }]);\n    return Picker;\n}();\n\n{\n    var style = document.createElement('style');\n    style.textContent = '.picker_wrapper.no_alpha .picker_alpha{display:none}.picker_wrapper.no_editor .picker_editor{position:absolute;z-index:-1;opacity:0}.picker_wrapper.no_cancel .picker_cancel{display:none}.layout_default.picker_wrapper{display:flex;flex-flow:row wrap;justify-content:space-between;align-items:stretch;font-size:10px;width:25em;padding:.5em}.layout_default.picker_wrapper input,.layout_default.picker_wrapper button{font-size:1rem}.layout_default.picker_wrapper>*{margin:.5em}.layout_default.picker_wrapper::before{content:\"\";display:block;width:100%;height:0;order:1}.layout_default .picker_slider,.layout_default .picker_selector{padding:1em}.layout_default .picker_hue{width:100%}.layout_default .picker_sl{flex:1 1 auto}.layout_default .picker_sl::before{content:\"\";display:block;padding-bottom:100%}.layout_default .picker_editor{order:1;width:6.5rem}.layout_default .picker_editor input{width:100%;height:100%}.layout_default .picker_sample{order:1;flex:1 1 auto}.layout_default .picker_done,.layout_default .picker_cancel{order:1}.picker_wrapper{box-sizing:border-box;background:#f2f2f2;box-shadow:0 0 0 1px silver;cursor:default;font-family:sans-serif;color:#444;pointer-events:auto}.picker_wrapper:focus{outline:none}.picker_wrapper button,.picker_wrapper input{box-sizing:border-box;border:none;box-shadow:0 0 0 1px silver;outline:none}.picker_wrapper button:focus,.picker_wrapper button:active,.picker_wrapper input:focus,.picker_wrapper input:active{box-shadow:0 0 2px 1px #1e90ff}.picker_wrapper button{padding:.4em .6em;cursor:pointer;background-color:#f5f5f5;background-image:linear-gradient(0deg, gainsboro, transparent)}.picker_wrapper button:active{background-image:linear-gradient(0deg, transparent, gainsboro)}.picker_wrapper button:hover{background-color:#fff}.picker_selector{position:absolute;z-index:1;display:block;-webkit-transform:translate(-50%, -50%);transform:translate(-50%, -50%);border:2px solid #fff;border-radius:100%;box-shadow:0 0 3px 1px #67b9ff;background:currentColor;cursor:pointer}.picker_slider .picker_selector{border-radius:2px}.picker_hue{position:relative;background-image:linear-gradient(90deg, red, yellow, lime, cyan, blue, magenta, red);box-shadow:0 0 0 1px silver}.picker_sl{position:relative;box-shadow:0 0 0 1px silver;background-image:linear-gradient(180deg, white, rgba(255, 255, 255, 0) 50%),linear-gradient(0deg, black, rgba(0, 0, 0, 0) 50%),linear-gradient(90deg, #808080, rgba(128, 128, 128, 0))}.picker_alpha,.picker_sample{position:relative;background:linear-gradient(45deg, lightgrey 25%, transparent 25%, transparent 75%, lightgrey 75%) 0 0/2em 2em,linear-gradient(45deg, lightgrey 25%, white 25%, white 75%, lightgrey 75%) 1em 1em/2em 2em;box-shadow:0 0 0 1px silver}.picker_alpha .picker_selector,.picker_sample .picker_selector{background:none}.picker_editor input{font-family:monospace;padding:.2em .4em}.picker_sample::before{content:\"\";position:absolute;display:block;width:100%;height:100%;background:currentColor}.picker_arrow{position:absolute;z-index:-1}.picker_wrapper.popup{position:absolute;z-index:2;margin:1.5em}.picker_wrapper.popup,.picker_wrapper.popup .picker_arrow::before,.picker_wrapper.popup .picker_arrow::after{background:#f2f2f2;box-shadow:0 0 10px 1px rgba(0,0,0,.4)}.picker_wrapper.popup .picker_arrow{width:3em;height:3em;margin:0}.picker_wrapper.popup .picker_arrow::before,.picker_wrapper.popup .picker_arrow::after{content:\"\";display:block;position:absolute;top:0;left:0;z-index:-99}.picker_wrapper.popup .picker_arrow::before{width:100%;height:100%;-webkit-transform:skew(45deg);transform:skew(45deg);-webkit-transform-origin:0 100%;transform-origin:0 100%}.picker_wrapper.popup .picker_arrow::after{width:150%;height:150%;box-shadow:none}.popup.popup_top{bottom:100%;left:0}.popup.popup_top .picker_arrow{bottom:0;left:0;-webkit-transform:rotate(-90deg);transform:rotate(-90deg)}.popup.popup_bottom{top:100%;left:0}.popup.popup_bottom .picker_arrow{top:0;left:0;-webkit-transform:rotate(90deg) scale(1, -1);transform:rotate(90deg) scale(1, -1)}.popup.popup_left{top:0;right:100%}.popup.popup_left .picker_arrow{top:0;right:0;-webkit-transform:scale(-1, 1);transform:scale(-1, 1)}.popup.popup_right{top:0;left:100%}.popup.popup_right .picker_arrow{top:0;left:0}';\n    document.documentElement.firstElementChild.appendChild(style);\n\n    Picker.StyleElement = style;\n}\n\nexport { Picker as default };\n"], "mappings": ";;;AAOA,IAAI,iBAAiB,SAAU,UAAU,aAAa;AACpD,MAAI,EAAE,oBAAoB,cAAc;AACtC,UAAM,IAAI,UAAU,mCAAmC;AAAA,EACzD;AACF;AAEA,IAAI,cAAc,2BAAY;AAC5B,WAAS,iBAAiB,QAAQ,OAAO;AACvC,aAAS,IAAI,GAAG,IAAI,MAAM,QAAQ,KAAK;AACrC,UAAI,aAAa,MAAM,CAAC;AACxB,iBAAW,aAAa,WAAW,cAAc;AACjD,iBAAW,eAAe;AAC1B,UAAI,WAAW,WAAY,YAAW,WAAW;AACjD,aAAO,eAAe,QAAQ,WAAW,KAAK,UAAU;AAAA,IAC1D;AAAA,EACF;AAEA,SAAO,SAAU,aAAa,YAAY,aAAa;AACrD,QAAI,WAAY,kBAAiB,YAAY,WAAW,UAAU;AAClE,QAAI,YAAa,kBAAiB,aAAa,WAAW;AAC1D,WAAO;AAAA,EACT;AACF,EAAE;AAEF,IAAI,gBAAgB,2BAAY;AAC9B,WAAS,cAAc,KAAK,GAAG;AAC7B,QAAI,OAAO,CAAC;AACZ,QAAI,KAAK;AACT,QAAI,KAAK;AACT,QAAI,KAAK;AAET,QAAI;AACF,eAAS,KAAK,IAAI,OAAO,QAAQ,EAAE,GAAG,IAAI,EAAE,MAAM,KAAK,GAAG,KAAK,GAAG,OAAO,KAAK,MAAM;AAClF,aAAK,KAAK,GAAG,KAAK;AAElB,YAAI,KAAK,KAAK,WAAW,EAAG;AAAA,MAC9B;AAAA,IACF,SAAS,KAAK;AACZ,WAAK;AACL,WAAK;AAAA,IACP,UAAE;AACA,UAAI;AACF,YAAI,CAAC,MAAM,GAAG,QAAQ,EAAG,IAAG,QAAQ,EAAE;AAAA,MACxC,UAAE;AACA,YAAI,GAAI,OAAM;AAAA,MAChB;AAAA,IACF;AAEA,WAAO;AAAA,EACT;AAEA,SAAO,SAAU,KAAK,GAAG;AACvB,QAAI,MAAM,QAAQ,GAAG,GAAG;AACtB,aAAO;AAAA,IACT,WAAW,OAAO,YAAY,OAAO,GAAG,GAAG;AACzC,aAAO,cAAc,KAAK,CAAC;AAAA,IAC7B,OAAO;AACL,YAAM,IAAI,UAAU,sDAAsD;AAAA,IAC5E;AAAA,EACF;AACF,EAAE;AAEF,OAAO,UAAU,aAAa,OAAO,UAAU,cAAc,SAAU,QAAQ;AAC3E,SAAO,KAAK,QAAQ,MAAM,MAAM;AACpC;AACA,OAAO,UAAU,WAAW,OAAO,UAAU,YAAY,SAAU,KAAK,KAAK;AACzE,MAAI,MAAM;AAAK,SAAO,IAAI,SAAS,KAAK;AACpC,UAAM,MAAM;AAAA,EAChB;AAAC,SAAO;AACZ;AAEA,IAAI,aAAa,EAAE,IAAI,SAAS,KAAK,SAAS,GAAG,SAAS,MAAM,UAAU,IAAI,SAAS,IAAI,SAAS,KAAK,QAAQ,KAAK,OAAO,KAAK,QAAQ,GAAG,QAAQ,KAAK,UAAU,MAAM,UAAU,KAAK,UAAU,KAAK,UAAU,KAAK,SAAS,MAAM,UAAU,IAAI,QAAQ,KAAK,UAAU,MAAM,OAAO,MAAM,UAAU,IAAI,SAAS,IAAI,QAAQ,KAAK,SAAS,MAAM,UAAU,KAAK,UAAU,MAAM,QAAQ,MAAM,UAAU,MAAM,SAAS,MAAM,UAAU,MAAM,OAAO,MAAM,UAAU,IAAI,QAAQ,MAAM,UAAU,MAAM,UAAU,MAAM,UAAU,MAAM,UAAU,MAAM,SAAS,KAAK,SAAS,MAAM,QAAQ,MAAM,SAAS,KAAK,UAAU,KAAK,UAAU,MAAM,UAAU,MAAM,OAAO,KAAK,UAAU,KAAK,OAAO,MAAM,UAAU,IAAI,SAAS,GAAG,OAAO,KAAK,UAAU,IAAI,UAAU,KAAK,QAAQ,MAAM,UAAU,KAAK,SAAS,MAAM,QAAQ,KAAK,UAAU,IAAI,SAAS,IAAI,KAAK,KAAK,SAAS,KAAK,UAAU,KAAK,OAAO,MAAM,SAAS,MAAM,OAAO,IAAI,UAAU,KAAK,SAAS,KAAK,UAAU,MAAM,SAAS,KAAK,UAAU,MAAM,UAAU,MAAM,QAAQ,MAAM,QAAQ,MAAM,UAAU,MAAM,UAAU,MAAM,UAAU,MAAM,UAAU,IAAI,MAAM,GAAG,QAAQ,MAAM,UAAU,IAAI,SAAS,MAAM,OAAO,KAAK,QAAQ,KAAK,UAAU,KAAK,QAAQ,MAAM,UAAU,MAAM,UAAU,KAAK,UAAU,MAAM,UAAU,IAAI,SAAS,KAAK,UAAU,MAAM,UAAU,MAAM,UAAU,KAAK,SAAS,MAAM,QAAQ,MAAM,QAAQ,KAAK,QAAQ,IAAI,QAAQ,GAAG,SAAS,GAAG,SAAS,KAAK,UAAU,KAAK,OAAO,MAAM,OAAO,KAAK,UAAU,MAAM,UAAU,MAAM,UAAU,MAAM,UAAU,MAAM,UAAU,MAAM,QAAQ,MAAM,QAAQ,IAAI,UAAU,KAAK,QAAQ,IAAI,UAAU,MAAM,UAAU,KAAK,SAAS,IAAI,UAAU,GAAG,MAAM,KAAK,UAAU,IAAI,UAAU,MAAM,UAAU,KAAK,SAAS,KAAK,SAAS,MAAM,UAAU,KAAK,OAAO,KAAK,UAAU,KAAK,UAAU,KAAK,UAAU,KAAK,UAAU,MAAM,UAAU,KAAK,OAAO,GAAG,SAAS,KAAK,UAAU,IAAI,UAAU,GAAG,SAAS,MAAM,UAAU,KAAK,QAAQ,MAAM,UAAU,IAAI,UAAU,KAAK,SAAS,KAAK,IAAI,KAAK,SAAS,GAAG,KAAK,MAAM,SAAS;AAEx9D,SAAS,SAAS,KAAK;AACnB,MAAI,OAAO,UAAU,SAAS,KAAK,UAAU,CAAC,MAAM,SAAY,UAAU,CAAC,IAAI;AAE/E,MAAI,MAAM,OAAO,IAAI,IAAI,QAAQ,IAAI,EAAE,QAAQ,OAAO,EAAE,EAAE,QAAQ,OAAO,EAAE,IAAI,IAAI,SAAS;AAC5F,SAAO,OAAO;AAClB;AAEA,IAAI,QAAQ,WAAY;AACpB,WAASA,OAAM,GAAG,GAAG,GAAG,GAAG;AACvB,mBAAe,MAAMA,MAAK;AAG1B,QAAI,OAAO;AACX,aAAS,YAAY,OAAO;AAExB,UAAI,MAAM,WAAW,KAAK,GAAG;AACzB,YAAI,mBAAmB,MAAM,MAAM,eAAe,EAAE,IAAI,MAAM,GAC1D,oBAAoB,cAAc,kBAAkB,CAAC,GACrD,IAAI,kBAAkB,CAAC,GACvB,IAAI,kBAAkB,CAAC,GACvB,IAAI,kBAAkB,CAAC,GACvB,KAAK,kBAAkB,CAAC;AAE5B,YAAI,OAAO,QAAW;AAClB,eAAK;AAAA,QACT;AAEA,aAAK;AACL,aAAK;AACL,aAAK;AACL,aAAK,OAAO,CAAC,GAAG,GAAG,GAAG,EAAE;AAAA,MAC5B,WAAW,MAAM,WAAW,KAAK,GAAG;AAChC,YAAI,oBAAoB,MAAM,MAAM,eAAe,EAAE,IAAI,MAAM,GAC3D,oBAAoB,cAAc,mBAAmB,CAAC,GACtD,KAAK,kBAAkB,CAAC,GACxB,KAAK,kBAAkB,CAAC,GACxB,KAAK,kBAAkB,CAAC,GACxB,MAAM,kBAAkB,CAAC;AAE7B,YAAI,QAAQ,QAAW;AACnB,gBAAM;AAAA,QACV;AAEA,aAAK,OAAO,CAAC,IAAI,IAAI,IAAI,GAAG;AAAA,MAChC,OAAO;AACH,YAAI,MAAM,WAAW,GAAG,GAAG;AACvB,eAAK,OAAOA,OAAM,SAAS,KAAK;AAAA,QACpC,OAAO;AACH,eAAK,OAAOA,OAAM,UAAU,KAAK,KAAKA,OAAM,SAAS,KAAK;AAAA,QAC9D;AAAA,MACJ;AAAA,IACJ;AAEA,QAAI,MAAM,OAAW;AAAA,aAAW,MAAM,QAAQ,CAAC,GAAG;AAC9C,WAAK,OAAO;AAAA,IAChB,WAAW,MAAM,QAAW;AACxB,UAAI,QAAQ,KAAK,KAAK;AACtB,UAAI,OAAO;AACP,oBAAY,MAAM,YAAY,CAAC;AAAA,MACnC;AAAA,IACJ,OAAO;AACH,WAAK,OAAO,CAAC,GAAG,GAAG,GAAG,MAAM,SAAY,IAAI,CAAC;AAAA,IACjD;AAAA,EACJ;AAEA,cAAYA,QAAO,CAAC;AAAA,IAChB,KAAK;AAAA,IACL,OAAO,SAAS,SAAS,OAAO;AAC5B,UAAI,MAAM,QAAQ,KAAK,OAAO,KAAK,KAAK,MAAM,GAAG,CAAC,GAC9C,OAAO,IAAI,IAAI,SAAU,GAAG,GAAG;AAC/B,eAAO,SAAS,GAAG,MAAM,IAAI,IAAI,CAAC;AAAA,MACtC,CAAC;AAED,aAAO,QAAQ,UAAU,OAAO,MAAM,SAAS,OAAO;AAAA,IAC1D;AAAA,EACJ,GAAG;AAAA,IACC,KAAK;AAAA,IACL,OAAO,SAAS,SAAS,OAAO;AAC5B,UAAI,QAAQ,CAAC,KAAK,KAAK,KAAK,CAAC,GACzB,OAAO,CAAC,IAAI,KAAK,KAAK,EAAE;AAE5B,UAAI,MAAM,QAAQ,KAAK,OAAO,KAAK,KAAK,MAAM,GAAG,CAAC,GAC9C,OAAO,IAAI,IAAI,SAAU,GAAG,GAAG;AAC/B,eAAO,SAAS,IAAI,MAAM,CAAC,GAAG,MAAM,IAAI,IAAI,CAAC,IAAI,KAAK,CAAC;AAAA,MAC3D,CAAC;AAED,aAAO,QAAQ,UAAU,OAAO,MAAM,SAAS,OAAO;AAAA,IAC1D;AAAA,EACJ,GAAG;AAAA,IACC,KAAK;AAAA,IACL,OAAO,SAAS,SAAS,OAAO;AAC5B,UAAI,MAAM,KAAK;AACf,aAAO,QAAQ,MAAM,IAAI,UAAU,GAAG,CAAC;AAAA,IAC3C;AAAA,EACJ,GAAG;AAAA,IACC,KAAK;AAAA,IACL,KAAK,SAAS,MAAM;AAChB,UAAI,KAAK,OAAO;AACZ,eAAO,KAAK;AAAA,MAChB;AACA,UAAI,CAAC,KAAK,OAAO;AACb,cAAM,IAAI,MAAM,iBAAiB;AAAA,MACrC;AAEA,aAAO,KAAK,QAAQA,OAAM,SAAS,KAAK,KAAK;AAAA,IACjD;AAAA,IACA,KAAK,SAAS,IAAI,KAAK;AACnB,UAAI,IAAI,WAAW,GAAG;AAClB,YAAI,CAAC,IAAI;AAAA,MACb;AAEA,WAAK,QAAQ;AACb,WAAK,QAAQ;AAAA,IACjB;AAAA,EACJ,GAAG;AAAA,IACC,KAAK;AAAA,IACL,KAAK,SAAS,MAAM;AAChB,aAAO,KAAK,SAAS;AAAA,IACzB;AAAA,EACJ,GAAG;AAAA,IACC,KAAK;AAAA,IACL,KAAK,SAAS,MAAM;AAChB,aAAO,KAAK,SAAS,IAAI;AAAA,IAC7B;AAAA,EACJ,GAAG;AAAA,IACC,KAAK;AAAA,IACL,KAAK,SAAS,MAAM;AAChB,UAAI,KAAK,OAAO;AACZ,eAAO,KAAK;AAAA,MAChB;AACA,UAAI,CAAC,KAAK,OAAO;AACb,cAAM,IAAI,MAAM,iBAAiB;AAAA,MACrC;AAEA,aAAO,KAAK,QAAQA,OAAM,SAAS,KAAK,KAAK;AAAA,IACjD;AAAA,IACA,KAAK,SAAS,IAAI,KAAK;AACnB,UAAI,IAAI,WAAW,GAAG;AAClB,YAAI,CAAC,IAAI;AAAA,MACb;AAEA,WAAK,QAAQ;AACb,WAAK,QAAQ;AAAA,IACjB;AAAA,EACJ,GAAG;AAAA,IACC,KAAK;AAAA,IACL,KAAK,SAAS,MAAM;AAChB,aAAO,KAAK,SAAS;AAAA,IACzB;AAAA,EACJ,GAAG;AAAA,IACC,KAAK;AAAA,IACL,KAAK,SAAS,MAAM;AAChB,aAAO,KAAK,SAAS,IAAI;AAAA,IAC7B;AAAA,EACJ,GAAG;AAAA,IACC,KAAK;AAAA,IACL,KAAK,SAAS,MAAM;AAChB,UAAI,MAAM,KAAK,MACX,MAAM,IAAI,IAAI,SAAU,GAAG,GAAG;AAC9B,eAAO,IAAI,IAAI,EAAE,SAAS,EAAE,IAAI,KAAK,MAAM,IAAI,GAAG,EAAE,SAAS,EAAE;AAAA,MACnE,CAAC;AAED,aAAO,MAAM,IAAI,IAAI,SAAU,GAAG;AAC9B,eAAO,EAAE,SAAS,GAAG,GAAG;AAAA,MAC5B,CAAC,EAAE,KAAK,EAAE;AAAA,IACd;AAAA,IACA,KAAK,SAAS,IAAI,KAAK;AACnB,WAAK,OAAOA,OAAM,SAAS,GAAG;AAAA,IAClC;AAAA,EACJ,CAAC,GAAG,CAAC;AAAA,IACD,KAAK;AAAA,IACL,OAAO,SAAS,SAAS,OAAO;AAE5B,UAAI,OAAO,MAAM,WAAW,GAAG,IAAI,MAAM,MAAM,CAAC,IAAI,OAAO,QAAQ,aAAa,KAAK,EAAE,QAAQ,sBAAsB,kBAAkB,EAAE,QAAQ,aAAa,MAAM;AAEpK,UAAI,CAAC,IAAI,MAAM,oBAAoB,GAAG;AAClC,cAAM,IAAI,MAAM,wBAAwB,KAAK;AAAA,MACjD;AAEA,UAAI,OAAO,IAAI,MAAM,4BAA4B,EAAE,MAAM,CAAC,EAAE,IAAI,SAAU,GAAG;AACzE,eAAO,SAAS,GAAG,EAAE;AAAA,MACzB,CAAC;AAED,WAAK,CAAC,IAAI,KAAK,CAAC,IAAI;AACpB,aAAO;AAAA,IACX;AAAA,EACJ,GAAG;AAAA,IACC,KAAK;AAAA,IACL,OAAO,SAAS,UAAU,OAAO;AAE7B,UAAI,OAAO,MAAM,YAAY,EAAE,QAAQ,MAAM,GAAG,EAAE,QAAQ,gBAAgB,EAAE,EAAE,QAAQ,OAAO,GAAG,EAAE,QAAQ,MAAM,GAAG,EAAE,MAAM,IAAI,CAAC,GAC5H,MAAM,WAAW,IAAI;AACzB,aAAO,QAAQ,SAAY,MAAMA,OAAM,SAAS,IAAI,QAAQ,OAAO,IAAI,EAAE,SAAS,GAAG,GAAG,CAAC;AAAA,IAC7F;AAAA,EACJ,GAAG;AAAA,IACC,KAAK;AAAA,IACL,OAAO,SAAS,SAAS,MAAM;AAC3B,UAAI,QAAQ,cAAc,MAAM,CAAC,GAC7B,IAAI,MAAM,CAAC,GACX,IAAI,MAAM,CAAC,GACX,IAAI,MAAM,CAAC,GACX,IAAI,MAAM,CAAC;AAEf,WAAK;AACL,WAAK;AACL,WAAK;AAEL,UAAI,MAAM,KAAK,IAAI,GAAG,GAAG,CAAC,GACtB,MAAM,KAAK,IAAI,GAAG,GAAG,CAAC;AAC1B,UAAI,IAAI,QACJ,IAAI,QACJ,KAAK,MAAM,OAAO;AAEtB,UAAI,QAAQ,KAAK;AACb,YAAI,IAAI;AAAA,MACZ,OAAO;AACH,YAAI,IAAI,MAAM;AACd,YAAI,IAAI,MAAM,KAAK,IAAI,MAAM,OAAO,KAAK,MAAM;AAC/C,gBAAQ,KAAK;AAAA,UACT,KAAK;AACD,iBAAK,IAAI,KAAK,KAAK,IAAI,IAAI,IAAI;AAAG;AAAA,UACtC,KAAK;AACD,iBAAK,IAAI,KAAK,IAAI;AAAE;AAAA,UACxB,KAAK;AACD,iBAAK,IAAI,KAAK,IAAI;AAAE;AAAA,QAC5B;AAEA,aAAK;AAAA,MACT;AAEA,aAAO,CAAC,GAAG,GAAG,GAAG,CAAC;AAAA,IACtB;AAAA,EACJ,GAAG;AAAA,IACC,KAAK;AAAA,IACL,OAAO,SAAS,SAAS,OAAO;AAC5B,UAAI,QAAQ,cAAc,OAAO,CAAC,GAC9B,IAAI,MAAM,CAAC,GACX,IAAI,MAAM,CAAC,GACX,IAAI,MAAM,CAAC,GACX,IAAI,MAAM,CAAC;AAEf,UAAI,IAAI,QACJ,IAAI,QACJ,IAAI;AAER,UAAI,MAAM,GAAG;AACT,YAAI,IAAI,IAAI;AAAA,MAChB,OAAO;AACH,YAAI,UAAU,SAASC,SAAQC,IAAGC,IAAG,GAAG;AACpC,cAAI,IAAI,EAAG,MAAK;AAChB,cAAI,IAAI,EAAG,MAAK;AAChB,cAAI,IAAI,IAAI,EAAG,QAAOD,MAAKC,KAAID,MAAK,IAAI;AACxC,cAAI,IAAI,IAAI,EAAG,QAAOC;AACtB,cAAI,IAAI,IAAI,EAAG,QAAOD,MAAKC,KAAID,OAAM,IAAI,IAAI,KAAK;AAClD,iBAAOA;AAAA,QACX;AAEA,YAAI,IAAI,IAAI,MAAM,KAAK,IAAI,KAAK,IAAI,IAAI,IAAI,GACxC,IAAI,IAAI,IAAI;AAEhB,YAAI,QAAQ,GAAG,GAAG,IAAI,IAAI,CAAC;AAC3B,YAAI,QAAQ,GAAG,GAAG,CAAC;AACnB,YAAI,QAAQ,GAAG,GAAG,IAAI,IAAI,CAAC;AAAA,MAC/B;AAEA,UAAI,OAAO,CAAC,IAAI,KAAK,IAAI,KAAK,IAAI,GAAG,EAAE,IAAI,KAAK,KAAK;AACrD,WAAK,CAAC,IAAI;AAEV,aAAO;AAAA,IACX;AAAA,EACJ,CAAC,CAAC;AACF,SAAOF;AACX,EAAE;AAEF,IAAI,cAAc,WAAY;AAC1B,WAASI,eAAc;AACnB,mBAAe,MAAMA,YAAW;AAEhC,SAAK,UAAU,CAAC;AAAA,EACpB;AAEA,cAAYA,cAAa,CAAC;AAAA,IACtB,KAAK;AAAA,IACL,OAAO,SAAS,IAAI,QAAQ,MAAM,SAAS;AACvC,aAAO,iBAAiB,MAAM,SAAS,KAAK;AAC5C,WAAK,QAAQ,KAAK;AAAA,QACd;AAAA,QACA;AAAA,QACA;AAAA,MACJ,CAAC;AAAA,IACL;AAAA,EACJ,GAAG;AAAA,IACC,KAAK;AAAA,IACL,OAAO,SAAS,OAAO,QAAQ,MAAM,SAAS;AAC1C,WAAK,UAAU,KAAK,QAAQ,OAAO,SAAU,GAAG;AAC5C,YAAI,UAAU;AACd,YAAI,UAAU,WAAW,EAAE,QAAQ;AAC/B,oBAAU;AAAA,QACd;AACA,YAAI,QAAQ,SAAS,EAAE,MAAM;AACzB,oBAAU;AAAA,QACd;AACA,YAAI,WAAW,YAAY,EAAE,SAAS;AAClC,oBAAU;AAAA,QACd;AAEA,YAAI,SAAS;AACT,UAAAA,aAAY,UAAU,EAAE,QAAQ,EAAE,MAAM,EAAE,OAAO;AAAA,QACrD;AACA,eAAO,CAAC;AAAA,MACZ,CAAC;AAAA,IACL;AAAA,EACJ,GAAG;AAAA,IACC,KAAK;AAAA,IACL,OAAO,SAAS,UAAU;AACtB,WAAK,QAAQ,QAAQ,SAAU,GAAG;AAC9B,eAAOA,aAAY,UAAU,EAAE,QAAQ,EAAE,MAAM,EAAE,OAAO;AAAA,MAC5D,CAAC;AACD,WAAK,UAAU,CAAC;AAAA,IACpB;AAAA,EACJ,CAAC,GAAG,CAAC;AAAA,IACD,KAAK;AAAA,IACL,OAAO,SAAS,UAAU,QAAQ,MAAM,SAAS;AAC7C,aAAO,oBAAoB,MAAM,SAAS,KAAK;AAAA,IACnD;AAAA,EACJ,CAAC,CAAC;AACF,SAAOA;AACX,EAAE;AAEF,SAAS,UAAU,YAAY;AAE3B,MAAI,MAAM,SAAS,cAAc,KAAK;AACtC,MAAI,YAAY;AAChB,SAAO,IAAI;AACf;AAEA,SAAS,UAAU,aAAa,MAAM,UAAU;AAC5C,MAAI,WAAW;AAEf,WAAS,MAAM,KAAK,KAAK,KAAK;AAC1B,WAAO,KAAK,IAAI,KAAK,KAAK,IAAI,KAAK,GAAG,CAAC;AAAA,EAC3C;AAEA,WAAS,OAAO,GAAG,MAAM,UAAU;AAC/B,QAAI,UAAU;AACV,iBAAW;AAAA,IACf;AACA,QAAI,CAAC,UAAU;AACX;AAAA,IACJ;AAEA,MAAE,eAAe;AAEjB,QAAI,SAAS,KAAK,sBAAsB,GACpC,IAAI,OAAO,OACX,IAAI,OAAO,QACX,IAAI,KAAK,SACT,IAAI,KAAK;AAEb,QAAI,OAAO,MAAM,IAAI,OAAO,MAAM,GAAG,CAAC,GAClC,OAAO,MAAM,IAAI,OAAO,KAAK,GAAG,CAAC;AAErC,aAAS,OAAO,GAAG,OAAO,CAAC;AAAA,EAC/B;AAEA,WAAS,QAAQ,GAAG,UAAU;AAC1B,QAAI,SAAS,EAAE,YAAY,SAAY,EAAE,QAAQ,EAAE;AACnD,QAAI,WAAW,GAAG;AACd,aAAO,GAAG,GAAG,QAAQ;AAAA,IACzB,OAAO;AACH,iBAAW;AAAA,IACf;AAAA,EACJ;AAEA,WAAS,QAAQ,GAAG,UAAU;AAC1B,QAAI,EAAE,QAAQ,WAAW,GAAG;AACxB,aAAO,GAAG,EAAE,QAAQ,CAAC,GAAG,QAAQ;AAAA,IACpC,OAAO;AACH,iBAAW;AAAA,IACf;AAAA,EACJ;AAEA,cAAY,IAAI,MAAM,aAAa,SAAU,GAAG;AAC5C,YAAQ,GAAG,IAAI;AAAA,EACnB,CAAC;AACD,cAAY,IAAI,MAAM,cAAc,SAAU,GAAG;AAC7C,YAAQ,GAAG,IAAI;AAAA,EACnB,CAAC;AACD,cAAY,IAAI,QAAQ,aAAa,OAAO;AAC5C,cAAY,IAAI,MAAM,aAAa,OAAO;AAC1C,cAAY,IAAI,QAAQ,WAAW,SAAU,GAAG;AAC5C,eAAW;AAAA,EACf,CAAC;AACD,cAAY,IAAI,MAAM,YAAY,SAAU,GAAG;AAC3C,eAAW;AAAA,EACf,CAAC;AACD,cAAY,IAAI,MAAM,eAAe,SAAU,GAAG;AAC9C,eAAW;AAAA,EACf,CAAC;AACL;AAEA,IAAI,YAAY;AAChB,IAAI,OAAO;AAEX,IAAI,YAAY;AAAhB,IACI,sBAAsB;AAD1B,IAEI,iBAAiB;AAErB,SAAS,EAAE,UAAU,SAAS;AAC1B,UAAQ,WAAW,UAAU,cAAc,QAAQ;AACvD;AAEA,SAAS,UAAU,GAAG;AAElB,IAAE,eAAe;AACjB,IAAE,gBAAgB;AACtB;AACA,SAAS,MAAM,QAAQ,QAAQ,MAAM,SAAS,MAAM;AAChD,SAAO,IAAI,QAAQ,WAAW,SAAU,GAAG;AACvC,QAAI,KAAK,QAAQ,EAAE,GAAG,KAAK,GAAG;AAC1B,UAAI,MAAM;AACN,kBAAU,CAAC;AAAA,MACf;AACA,cAAQ,CAAC;AAAA,IACb;AAAA,EACJ,CAAC;AACL;AAEA,IAAI,SAAS,WAAY;AACrB,WAASC,QAAO,SAAS;AACrB,mBAAe,MAAMA,OAAM;AAG3B,SAAK,WAAW;AAAA,MAEZ,OAAO;AAAA,MACP,QAAQ;AAAA,MACR,OAAO;AAAA,MACP,QAAQ;AAAA,MACR,cAAc;AAAA,MACd,cAAc;AAAA,MACd,cAAc;AAAA,IAClB;AAEA,SAAK,UAAU,IAAI,YAAY;AAE/B,SAAK,WAAW;AAEhB,SAAK,SAAS;AAEd,SAAK,SAAS;AAEd,SAAK,UAAU;AAEf,SAAK,WAAW,OAAO;AAAA,EAC3B;AAEA,cAAYA,SAAQ,CAAC;AAAA,IACjB,KAAK;AAAA,IACL,OAAO,SAAS,WAAW,SAAS;AAChC,UAAI,QAAQ;AAEZ,UAAI,CAAC,SAAS;AACV;AAAA,MACJ;AACA,UAAI,WAAW,KAAK;AAEpB,eAAS,SAAS,QAAQ,QAAQ,UAAU;AACxC,iBAAS,OAAO,QAAQ;AACpB,cAAI,YAAY,SAAS,QAAQ,GAAG,KAAK,GAAG;AACxC;AAAA,UACJ;AAEA,iBAAO,GAAG,IAAI,OAAO,GAAG;AAAA,QAC5B;AAAA,MACJ;AAEA,UAAI,mBAAmB,aAAa;AAChC,iBAAS,SAAS;AAAA,MACtB,OAAO;AAEH,YAAI,SAAS,UAAU,QAAQ,UAAU,SAAS,WAAW,QAAQ,QAAQ;AACzE,eAAK,QAAQ,OAAO,SAAS,MAAM;AACnC,eAAK,eAAe;AAAA,QACxB;AAEA,iBAAS,SAAS,QAAQ;AAE1B,YAAI,QAAQ,UAAU;AAClB,eAAK,WAAW,QAAQ;AAAA,QAC5B;AACA,YAAI,QAAQ,QAAQ;AAChB,eAAK,SAAS,QAAQ;AAAA,QAC1B;AACA,YAAI,QAAQ,QAAQ;AAChB,eAAK,SAAS,QAAQ;AAAA,QAC1B;AACA,YAAI,QAAQ,SAAS;AACjB,eAAK,UAAU,QAAQ;AAAA,QAC3B;AAEA,YAAI,MAAM,QAAQ,SAAS,QAAQ;AACnC,YAAI,KAAK;AACL,eAAK,UAAU,GAAG;AAAA,QACtB;AAAA,MACJ;AAEA,UAAI,SAAS,SAAS;AACtB,UAAI,UAAU,SAAS,SAAS,CAAC,KAAK,cAAc;AAEhD,YAAI,YAAY,SAASC,WAAU,GAAG;AAClC,iBAAO,MAAM,YAAY,CAAC;AAAA,QAC9B;AAEA,aAAK,QAAQ,IAAI,QAAQ,SAAS,SAAS;AAE3C,cAAM,KAAK,SAAS,QAAQ,CAAC,KAAK,YAAY,OAAO,GAAG,SAAS;AAEjE,aAAK,eAAe;AAAA,MACxB,WAAW,QAAQ,UAAU,CAAC,SAAS,OAAO;AAC1C,aAAK,KAAK;AAAA,MACd;AAAA,IACJ;AAAA,EACJ,GAAG;AAAA,IACC,KAAK;AAAA,IACL,OAAO,SAAS,YAAY,GAAG;AAC3B,UAAI,KAAK,KAAK,GAAG;AAEb,aAAK,EAAE,eAAe;AAEtB,aAAK,SAAS,OAAO,MAAM,gBAAgB;AAE3C,YAAI,UAAU,KAAK,EAAE,SAAS,YAAY,KAAK,WAAW,KAAK;AAC/D,mBAAW,WAAY;AACnB,iBAAO,QAAQ,MAAM;AAAA,QACzB,GAAG,GAAG;AAEN,YAAI,KAAK,QAAQ;AACb,eAAK,OAAO,KAAK,MAAM;AAAA,QAC3B;AAAA,MACJ;AAAA,IACJ;AAAA,EACJ,GAAG;AAAA,IACC,KAAK;AAAA,IACL,OAAO,SAAS,aAAa,GAAG;AAC5B,UAAI,QAAQ,KAAK,EAAE;AACnB,UAAI,SAAS;AAEb,UAAI,CAAC,GAAG;AACJ,iBAAS;AAAA,MACb,WAAW,UAAU,uBAAuB,UAAU,gBAAgB;AAElE,YAAI,aAAa,KAAK,oBAAoB,KAAK;AAC/C,YAAI,EAAE,YAAY,WAAW;AACzB,mBAAS;AAAA,QACb;AAAA,MACJ,OAAO;AAEH,kBAAU,CAAC;AAEX,iBAAS;AAAA,MACb;AAEA,UAAI,UAAU,KAAK,KAAK,GAAG;AACvB,aAAK,SAAS,OAAO,MAAM,gBAAgB;AAE3C,YAAI,UAAU,qBAAqB;AAC/B,eAAK,SAAS,OAAO,MAAM;AAAA,QAC/B;AAEA,YAAI,KAAK,SAAS;AACd,eAAK,QAAQ,KAAK,MAAM;AAAA,QAC5B;AAAA,MACJ;AAAA,IACJ;AAAA,EACJ,GAAG;AAAA,IACC,KAAK;AAAA,IACL,OAAO,SAAS,UAAU,SAAS,MAAM;AAErC,WAAK,aAAa;AAElB,WAAK,WAAW,OAAO;AACvB,UAAI,MAAM;AACN,aAAK,YAAY;AAAA,MACrB;AAAA,IACJ;AAAA,EACJ,GAAG;AAAA,IACC,KAAK;AAAA,IACL,OAAO,SAAS,SAAS,OAAO,QAAQ;AACpC,WAAK,UAAU,OAAO,EAAE,OAAe,CAAC;AAAA,IAC5C;AAAA,EACJ,GAAG;AAAA,IACC,KAAK;AAAA,IACL,OAAO,SAAS,UAAU,OAAO,OAAO;AACpC,UAAI,OAAO,UAAU,UAAU;AAC3B,gBAAQ,MAAM,KAAK;AAAA,MACvB;AACA,UAAI,CAAC,OAAO;AACR;AAAA,MACJ;AAEA,cAAQ,SAAS,CAAC;AAClB,UAAI,IAAI;AACR,UAAI;AAEA,YAAI,IAAI,MAAM,KAAK;AAAA,MACvB,SAAS,IAAI;AACT,YAAI,MAAM,cAAc;AACpB;AAAA,QACJ;AACA,cAAM;AAAA,MACV;AAEA,UAAI,CAAC,KAAK,SAAS,OAAO;AACtB,YAAI,OAAO,EAAE;AACb,aAAK,CAAC,IAAI;AACV,UAAE,OAAO;AAAA,MACb;AACA,WAAK,SAAS,KAAK,QAAQ;AAC3B,WAAK,SAAS,MAAM,MAAM,MAAM,MAAM,KAAK;AAAA,IAC/C;AAAA,EACJ,GAAG;AAAA,IACC,KAAK;AAAA,IACL,OAAO,SAAS,UAAU,QAAQ,QAAQ;AACtC,WAAK,SAAS,QAAQ,MAAM;AAAA,IAChC;AAAA,EACJ,GAAG;AAAA,IACC,KAAK;AAAA,IACL,OAAO,SAAS,OAAO;AACnB,UAAI,SAAS,KAAK,SAAS;AAC3B,UAAI,CAAC,QAAQ;AACT,eAAO;AAAA,MACX;AAEA,UAAI,KAAK,YAAY;AACjB,YAAI,UAAU,KAAK,WAAW,IAAI;AAElC,aAAK,aAAa;AAElB,eAAO;AAAA,MACX;AAEA,UAAI,OAAO,KAAK,SAAS,YAAY;AACrC,UAAI,UAAU,UAAU,IAAI;AAE5B,WAAK,aAAa;AAClB,WAAK,QAAQ,EAAE,eAAe,OAAO;AACrC,WAAK,SAAS,EAAE,cAAc,OAAO;AACrC,WAAK,QAAQ,EAAE,iBAAiB,OAAO;AACvC,WAAK,WAAW,EAAE,wBAAwB,OAAO;AACjD,WAAK,aAAa,EAAE,kBAAkB,OAAO;AAC7C,WAAK,WAAW,EAAE,uBAAuB,OAAO;AAChD,WAAK,aAAa,EAAE,yBAAyB,OAAO;AAEpD,cAAQ,UAAU,IAAI,YAAY,KAAK,SAAS,MAAM;AACtD,UAAI,CAAC,KAAK,SAAS,OAAO;AACtB,gBAAQ,UAAU,IAAI,UAAU;AAAA,MACpC;AACA,UAAI,CAAC,KAAK,SAAS,QAAQ;AACvB,gBAAQ,UAAU,IAAI,WAAW;AAAA,MACrC;AACA,UAAI,CAAC,KAAK,SAAS,cAAc;AAC7B,gBAAQ,UAAU,IAAI,WAAW;AAAA,MACrC;AACA,WAAK,SAAS,WAAY;AACtB,eAAO,QAAQ,UAAU,IAAI,OAAO;AAAA,MACxC,CAAC;AAED,WAAK,aAAa;AAElB,UAAI,KAAK,QAAQ;AACb,aAAK,UAAU;AAAA,MACnB,OAAO;AACH,aAAK,UAAU,KAAK,SAAS,YAAY;AAAA,MAC7C;AACA,WAAK,YAAY;AAEjB,aAAO;AAAA,IACX;AAAA,EACJ,GAAG;AAAA,IACC,KAAK;AAAA,IACL,OAAO,SAAS,OAAO;AACnB,aAAO,KAAK,WAAW,KAAK;AAAA,IAChC;AAAA,EACJ,GAAG;AAAA,IACC,KAAK;AAAA,IACL,OAAO,SAAS,UAAU;AACtB,WAAK,QAAQ,QAAQ;AACrB,UAAI,KAAK,YAAY;AACjB,aAAK,SAAS,OAAO,YAAY,KAAK,UAAU;AAAA,MACpD;AAAA,IACJ;AAAA,EACJ,GAAG;AAAA,IACC,KAAK;AAAA,IACL,OAAO,SAAS,cAAc;AAC1B,UAAI,SAAS;AAEb,UAAI,OAAO,MACP,MAAM,KAAK,YACX,SAAS,KAAK;AAElB,eAAS,SAAS,QAAQ,MAAM,SAAS;AACrC,eAAO,IAAI,QAAQ,MAAM,OAAO;AAAA,MACpC;AAEA,eAAS,KAAK,SAAS,SAAU,GAAG;AAChC,eAAO,EAAE,eAAe;AAAA,MAC5B,CAAC;AAED,gBAAU,QAAQ,KAAK,OAAO,SAAU,GAAG,GAAG;AAC1C,eAAO,KAAK,SAAS,CAAC;AAAA,MAC1B,CAAC;AAED,gBAAU,QAAQ,KAAK,QAAQ,SAAU,GAAG,GAAG;AAC3C,eAAO,KAAK,SAAS,MAAM,GAAG,IAAI,CAAC;AAAA,MACvC,CAAC;AAED,UAAI,KAAK,SAAS,OAAO;AACrB,kBAAU,QAAQ,KAAK,OAAO,SAAU,GAAG,GAAG;AAC1C,iBAAO,KAAK,SAAS,MAAM,MAAM,MAAM,IAAI,CAAC;AAAA,QAChD,CAAC;AAAA,MACL;AAEA,UAAI,YAAY,KAAK;AACrB;AACI,iBAAS,WAAW,SAAS,SAAU,GAAG;AACtC,eAAK,UAAU,KAAK,OAAO,EAAE,YAAY,MAAM,cAAc,KAAK,CAAC;AAAA,QACvE,CAAC;AAED,iBAAS,WAAW,SAAS,SAAU,GAAG;AACtC,cAAI,QAAQ;AAEZ,cAAI,MAAM,mBAAmB,MAAM,cAAc;AAC7C,kBAAM,OAAO;AAAA,UACjB;AAAA,QACJ,CAAC;AAAA,MACL;AAEA,WAAK,SAAS,WAAY;AAEtB,YAAI,kBAAkB,SAASC,iBAAgB,GAAG;AAC9C,iBAAO,OAAO,aAAa,CAAC;AAAA,QAChC;AAEA,iBAAS,QAAQ,qBAAqB,eAAe;AACrD,iBAAS,QAAQ,gBAAgB,eAAe;AAChD,cAAM,QAAQ,KAAK,CAAC,OAAO,QAAQ,GAAG,eAAe;AAErD,YAAI,aAAa,SAASC,YAAW,GAAG;AACpC,iBAAO,mBAAmB,EAAE;AAAA,QAChC;AACA,iBAAS,KAAK,qBAAqB,UAAU;AAE7C,iBAAS,KAAK,gBAAgB,UAAU;AAExC,iBAAS,OAAO,YAAY,SAAS,eAAe;AAAA,MACxD,CAAC;AAED,UAAI,cAAc,SAASC,aAAY,GAAG;AACtC,eAAO,SAAS,WAAY;AACxB,iBAAO,OAAO,aAAa,CAAC;AAAA,QAChC,CAAC;AACD,YAAI,OAAO,QAAQ;AACf,iBAAO,OAAO,OAAO,MAAM;AAAA,QAC/B;AAAA,MACJ;AACA,eAAS,KAAK,UAAU,SAAS,WAAW;AAC5C,YAAM,QAAQ,KAAK,CAAC,OAAO,GAAG,WAAW;AAAA,IAC7C;AAAA,EACJ,GAAG;AAAA,IACC,KAAK;AAAA,IACL,OAAO,SAAS,eAAe;AAC3B,UAAI,SAAS,KAAK,SAAS,QACvB,MAAM,KAAK;AAEf,UAAI,WAAW,IAAI,YAAY;AAC3B,eAAO,YAAY,GAAG;AAAA,MAC1B;AAEA,WAAK,SAAS,SAAU,OAAO;AAE3B,YAAI,iBAAiB,MAAM,EAAE,aAAa,UAAU;AAChD,iBAAO,MAAM,WAAW;AAAA,QAC5B;AAEA,YAAI,WAAW,UAAU,OAAO,gBAAgB,WAAW;AAE3D,SAAC,aAAa,gBAAgB,cAAc,aAAa,EAAE,QAAQ,SAAU,GAAG;AAE5E,cAAI,MAAM,UAAU;AAChB,gBAAI,UAAU,IAAI,CAAC;AAAA,UACvB,OAAO;AACH,gBAAI,UAAU,OAAO,CAAC;AAAA,UAC1B;AAAA,QACJ,CAAC;AAED,YAAI,UAAU,IAAI,QAAQ;AAAA,MAC9B,CAAC;AAAA,IACL;AAAA,EACJ,GAAG;AAAA,IACC,KAAK;AAAA,IACL,OAAO,SAAS,SAAS,GAAG,GAAG,GAAG,GAAG,OAAO;AACxC,cAAQ,SAAS,CAAC;AAElB,UAAI,MAAM,KAAK,QACX,OAAO,IAAI;AAEf,OAAC,GAAG,GAAG,GAAG,CAAC,EAAE,QAAQ,SAAU,GAAG,GAAG;AACjC,YAAI,KAAK,MAAM,GAAG;AACd,eAAK,CAAC,IAAI;AAAA,QACd;AAAA,MACJ,CAAC;AACD,UAAI,OAAO;AAEX,WAAK,UAAU,KAAK;AAEpB,UAAI,KAAK,YAAY,CAAC,MAAM,QAAQ;AAChC,aAAK,SAAS,GAAG;AAAA,MACrB;AAAA,IACJ;AAAA,EACJ,GAAG;AAAA,IACC,KAAK;AAAA,IACL,OAAO,SAAS,UAAU,OAAO;AAC7B,UAAI,CAAC,KAAK,YAAY;AAClB;AAAA,MACJ;AACA,cAAQ,SAAS,CAAC;AAElB,UAAI,MAAM,KAAK,QACX,MAAM,IAAI,MACV,SAAS,SAAS,IAAI,CAAC,IAAI,OAAO,gBAClC,SAAS,IAAI,WACb,UAAU,IAAI;AAElB,UAAI,MAAM,KAAK,OACX,OAAO,KAAK,QACZ,MAAM,KAAK,OACX,SAAS,EAAE,oBAAoB,GAAG,GAClC,UAAU,EAAE,oBAAoB,IAAI,GACpC,SAAS,EAAE,oBAAoB,GAAG;AAEtC,eAAS,KAAK,QAAQ,OAAO,MAAM;AAC/B,cAAM,MAAM,OAAO,OAAO,MAAM;AAAA,MACpC;AACA,eAAS,KAAK,QAAQ,OAAO,MAAM;AAC/B,cAAM,MAAM,MAAM,OAAO,MAAM;AAAA,MACnC;AAEA,WAAK,KAAK,QAAQ,IAAI,CAAC,CAAC;AAExB,WAAK,OAAO,MAAM,kBAAkB,KAAK,MAAM,MAAM,QAAQ;AAE7D,WAAK,MAAM,SAAS,IAAI,CAAC,CAAC;AAC1B,WAAK,MAAM,SAAS,IAAI,IAAI,CAAC,CAAC;AAE9B,WAAK,MAAM,QAAQ;AAEnB,WAAK,KAAK,QAAQ,IAAI,IAAI,CAAC,CAAC;AAE5B,UAAI,SAAS,QACT,SAAS,OAAO,QAAQ,OAAO,MAAM,EAAE,QAAQ,KAAK,MAAM,GAC1D,KAAK,qBAAqB,CAAC,QAAQ,MAAM,IAAI;AAEjD,WAAK,MAAM,MAAM,aAAa,KAAK,OAAO;AAE1C,UAAI,CAAC,MAAM,YAAY;AACnB,YAAI,SAAS,KAAK,SAAS,cACvB,QAAQ,KAAK,SAAS;AAE1B,YAAI,QAAQ;AACZ,gBAAQ,QAAQ;AAAA,UACZ,KAAK;AACD,oBAAQ,IAAI,SAAS,KAAK;AAAE;AAAA,UAChC,KAAK;AACD,oBAAQ,IAAI,SAAS,KAAK;AAAE;AAAA,UAChC;AACI,oBAAQ,IAAI,SAAS,KAAK;AAAA,QAClC;AACA,aAAK,SAAS,QAAQ;AAAA,MAC1B;AAEA,WAAK,WAAW,MAAM,QAAQ;AAAA,IAClC;AAAA,EACJ,GAAG;AAAA,IACC,KAAK;AAAA,IACL,OAAO,SAAS,SAAS,UAAU,YAAY;AAC3C,UAAI,KAAK,SAAS,UAAU,KAAK,SAAS,OAAO;AAC7C,oBAAY,SAAS,KAAK,SAAS,KAAK;AAAA,MAC5C,OAAO;AACH,sBAAc,WAAW;AAAA,MAC7B;AAAA,IACJ;AAAA,EACJ,GAAG;AAAA,IACC,KAAK;AAAA,IACL,OAAO,SAAS,WAAW,WAAW;AAClC,UAAI,MAAM,KAAK;AACf,UAAI,CAAC,KAAK;AACN,eAAO;AAAA,MACX;AAEA,UAAI,eAAe,YAAY,KAAK,QAChC,SAAS,IAAI,MAAM,YAAY;AAEnC,UAAI,QAAQ;AACR,YAAI,MAAM,UAAU;AAAA,MACxB;AACA,aAAO;AAAA,IACX;AAAA,EACJ,CAAC,CAAC;AACF,SAAOJ;AACX,EAAE;AAEF;AACQ,UAAQ,SAAS,cAAc,OAAO;AAC1C,QAAM,cAAc;AACpB,WAAS,gBAAgB,kBAAkB,YAAY,KAAK;AAE5D,SAAO,eAAe;AAC1B;AALQ;", "names": ["Color", "hue2rgb", "p", "q", "EventBucket", "Picker", "openProxy", "popupCloseProxy", "<PERSON><PERSON><PERSON><PERSON>", "onDoneProxy"]}