{"version": 3, "sources": ["../../short-unique-id/src/index.ts", "../../short-unique-id/package.json"], "sourcesContent": ["/**\n * @packageDocumentation\n **/\n\n// Copyright 2017-2025 the Short Unique ID authors. All rights reserved. Apache 2.0 license.\n\n// @ts-ignore\nimport { version } from '../package.json';\n\nexport interface ShortUniqueIdRanges {\n  [k: string]: [number, number];\n}\n\nexport interface ShortUniqueIdRangesMap {\n  [k: string]: ShortUniqueIdRanges;\n}\n\nexport type ShortUniqueIdDefaultDictionaries =\n  | 'number'\n  | 'alpha'\n  | 'alpha_lower'\n  | 'alpha_upper'\n  | 'alphanum'\n  | 'alphanum_lower'\n  | 'alphanum_upper'\n  | 'hex';\n\n/**\n * ```js\n * {\n *   dictionary: ['z', 'a', 'p', 'h', 'o', 'd', ...],\n *   shuffle: false,\n *   debug: false,\n *   length: 6,\n * }\n * ```\n * <br/>\n * @see {@link DEFAULT_OPTIONS}\n */\nexport interface ShortUniqueIdOptions {\n  /** User-defined character dictionary */\n  dictionary: string[] | ShortUniqueIdDefaultDictionaries;\n\n  /** If true, sequentialUUID use the dictionary in the given order */\n  shuffle: boolean;\n\n  /** If true the instance will console.log useful info */\n  debug: boolean;\n\n  /** From 1 to infinity, the length you wish your UUID to be */\n  length: number;\n\n  /** From 0 to infinity, the current value for the sequential UUID counter */\n  counter: number;\n}\n\n/**\n * 6 was chosen as the default UUID length since for most cases\n * it will be more than aptly suitable to provide millions of UUIDs\n * with a very low probability of producing a duplicate UUID.\n *\n * For example, with a dictionary including digits from 0 to 9,\n * as well as the alphabet from a to z both in UPPER and lower case,\n * the probability of generating a duplicate in 1,000,000 rounds\n * is ~0.00000002, or about 1 in 50,000,000.\n */\nexport const DEFAULT_UUID_LENGTH: number = 6;\n\nexport const DEFAULT_OPTIONS: ShortUniqueIdOptions = {\n  dictionary: 'alphanum',\n  shuffle: true,\n  debug: false,\n  length: DEFAULT_UUID_LENGTH,\n  counter: 0,\n};\n\n/**\n * Generate random or sequential UUID of any length.\n *\n * ### Use as module\n *\n * ```js\n * // Deno (web module) Import\n * import ShortUniqueId from 'https://cdn.jsdelivr.net/npm/short-unique-id@latest/src/index.ts';\n *\n * // ES6 / TypeScript Import\n * import ShortUniqueId from 'short-unique-id';\n *\n * // or Node.js require\n * const ShortUniqueId = require('short-unique-id');\n *\n * // Instantiate\n * const uid = new ShortUniqueId();\n *\n * // Random UUID\n * console.log(uid.rnd());\n *\n * // Sequential UUID\n * console.log(uid.seq());\n * ```\n *\n * ### Use in browser\n *\n * ```html\n * <!-- Import -->\n * <script src=\"https://cdn.jsdelivr.net/npm/short-unique-id@latest/dist/short-unique-id.min.js\"></script>\n *\n * <!-- Usage -->\n * <script>\n *   // Instantiate\n *   var uid = new ShortUniqueId();\n *\n *   // Random UUID\n *   document.write(uid.rnd());\n *\n *   // Sequential UUID\n *   document.write(uid.seq());\n * </script>\n * ```\n *\n * ### Options\n *\n * Options can be passed when instantiating `uid`:\n *\n * ```js\n * const options = { ... };\n *\n * const uid = new ShortUniqueId(options);\n * ```\n *\n * For more information take a look at the [ShortUniqueIdOptions type definition](/interfaces/shortuniqueidoptions.html).\n */\nexport default class ShortUniqueId {\n  /** @hidden */\n  static default: typeof ShortUniqueId = ShortUniqueId;\n\n  public counter: number;\n  public debug: boolean;\n  public dict: string[];\n  public version: string;\n  public dictIndex = 0;\n  public dictRange: number[] = [];\n  public lowerBound = 0;\n  public upperBound = 0;\n  public dictLength = 0;\n  public uuidLength: number;\n\n  protected _digit_first_ascii = 48;\n  protected _digit_last_ascii = 58;\n  protected _alpha_lower_first_ascii = 97;\n  protected _alpha_lower_last_ascii = 123;\n  protected _hex_last_ascii = 103;\n  protected _alpha_upper_first_ascii = 65;\n  protected _alpha_upper_last_ascii = 91;\n\n  protected _number_dict_ranges: ShortUniqueIdRanges = {\n    digits: [this._digit_first_ascii, this._digit_last_ascii],\n  };\n\n  protected _alpha_dict_ranges: ShortUniqueIdRanges = {\n    lowerCase: [this._alpha_lower_first_ascii, this._alpha_lower_last_ascii],\n    upperCase: [this._alpha_upper_first_ascii, this._alpha_upper_last_ascii],\n  };\n\n  protected _alpha_lower_dict_ranges: ShortUniqueIdRanges = {\n    lowerCase: [this._alpha_lower_first_ascii, this._alpha_lower_last_ascii],\n  };\n\n  protected _alpha_upper_dict_ranges: ShortUniqueIdRanges = {\n    upperCase: [this._alpha_upper_first_ascii, this._alpha_upper_last_ascii],\n  };\n\n  protected _alphanum_dict_ranges: ShortUniqueIdRanges = {\n    digits: [this._digit_first_ascii, this._digit_last_ascii],\n    lowerCase: [this._alpha_lower_first_ascii, this._alpha_lower_last_ascii],\n    upperCase: [this._alpha_upper_first_ascii, this._alpha_upper_last_ascii],\n  };\n\n  protected _alphanum_lower_dict_ranges: ShortUniqueIdRanges = {\n    digits: [this._digit_first_ascii, this._digit_last_ascii],\n    lowerCase: [this._alpha_lower_first_ascii, this._alpha_lower_last_ascii],\n  };\n\n  protected _alphanum_upper_dict_ranges: ShortUniqueIdRanges = {\n    digits: [this._digit_first_ascii, this._digit_last_ascii],\n    upperCase: [this._alpha_upper_first_ascii, this._alpha_upper_last_ascii],\n  };\n\n  protected _hex_dict_ranges: ShortUniqueIdRanges = {\n    decDigits: [this._digit_first_ascii, this._digit_last_ascii],\n    alphaDigits: [this._alpha_lower_first_ascii, this._hex_last_ascii],\n  };\n\n  protected _dict_ranges: ShortUniqueIdRangesMap = {\n    _number_dict_ranges: this._number_dict_ranges,\n    _alpha_dict_ranges: this._alpha_dict_ranges,\n    _alpha_lower_dict_ranges: this._alpha_lower_dict_ranges,\n    _alpha_upper_dict_ranges: this._alpha_upper_dict_ranges,\n    _alphanum_dict_ranges: this._alphanum_dict_ranges,\n    _alphanum_lower_dict_ranges: this._alphanum_lower_dict_ranges,\n    _alphanum_upper_dict_ranges: this._alphanum_upper_dict_ranges,\n    _hex_dict_ranges: this._hex_dict_ranges,\n  };\n\n  /* tslint:disable consistent-return */\n  protected log = (...args: unknown[]): void => {\n    const finalArgs = [...args];\n    finalArgs[0] = `[short-unique-id] ${args[0]}`;\n    /* tslint:disable no-console */\n    if (this.debug === true) {\n      if (typeof console !== 'undefined' && console !== null) {\n        console.log(...finalArgs);\n        return;\n      }\n    }\n    /* tslint:enable no-console */\n  };\n  /* tslint:enable consistent-return */\n\n  protected _normalizeDictionary = (\n    dictionary: string[] | ShortUniqueIdDefaultDictionaries,\n    shuffle?: boolean,\n  ): string[] => {\n    let finalDict: string[];\n\n    if (dictionary && Array.isArray(dictionary) && dictionary.length > 1) {\n      finalDict = dictionary as string[];\n    } else {\n      finalDict = [];\n      this.dictIndex = 0;\n\n      const rangesName = `_${dictionary as ShortUniqueIdDefaultDictionaries}_dict_ranges`;\n      const ranges = this._dict_ranges[rangesName];\n\n      // Pre-allocate array capacity when possible for better performance\n      let capacity = 0;\n      for (const [, rangeValue] of Object.entries(ranges)) {\n        const [lower, upper] = rangeValue;\n        capacity += Math.abs(upper - lower);\n      }\n      finalDict = new Array(capacity);\n\n      let dictIdx = 0;\n      for (const [, rangeTypeValue] of Object.entries(ranges)) {\n        this.dictRange = rangeTypeValue;\n        this.lowerBound = this.dictRange[0];\n        this.upperBound = this.dictRange[1];\n        const isAscending = this.lowerBound <= this.upperBound;\n        const start = this.lowerBound;\n        const end = this.upperBound;\n\n        if (isAscending) {\n          for (let i = start; i < end; i++) {\n            finalDict[dictIdx++] = String.fromCharCode(i);\n            this.dictIndex = i;\n          }\n        } else {\n          for (let i = start; i > end; i--) {\n            finalDict[dictIdx++] = String.fromCharCode(i);\n            this.dictIndex = i;\n          }\n        }\n      }\n      finalDict.length = dictIdx; // Trim any excess capacity\n    }\n\n    if (shuffle) {\n      // Fisher-Yates shuffle - more efficient than sort-based shuffle\n      const len = finalDict.length;\n      for (let i = len - 1; i > 0; i--) {\n        const j = Math.floor(Math.random() * (i + 1));\n        [finalDict[i], finalDict[j]] = [finalDict[j], finalDict[i]];\n      }\n    }\n\n    return finalDict;\n  };\n\n  /** Change the dictionary after initialization. */\n  setDictionary = (\n    dictionary: string[] | ShortUniqueIdDefaultDictionaries,\n    shuffle?: boolean,\n  ): void => {\n    this.dict = this._normalizeDictionary(dictionary, shuffle);\n\n    // Cache Dictionary Length for future usage.\n    this.dictLength = this.dict.length;\n\n    // Reset internal counter.\n    this.setCounter(0);\n  };\n\n  seq = (): string => {\n    return this.sequentialUUID();\n  };\n\n  /**\n   * Generates UUID based on internal counter that's incremented after each ID generation.\n   * @alias `const uid = new ShortUniqueId(); uid.seq();`\n   */\n  sequentialUUID = (): string => {\n    // Cache these values for performance\n    const dictLen = this.dictLength;\n    const dict = this.dict;\n    let counterDiv = this.counter;\n\n    // Pre-allocate array for better performance with string building\n    const idParts = [];\n\n    // Generate ID based on counter\n    do {\n      const counterRem = counterDiv % dictLen;\n      counterDiv = Math.trunc(counterDiv / dictLen);\n      idParts.push(dict[counterRem]);\n    } while (counterDiv !== 0);\n\n    // Reverse and join in one operation is more efficient than prepending to string\n    const id = idParts.join('');\n\n    this.counter += 1;\n\n    return id;\n  };\n\n  rnd = (uuidLength: number = this.uuidLength || DEFAULT_UUID_LENGTH): string => {\n    return this.randomUUID(uuidLength);\n  };\n\n  /**\n   * Generates UUID by creating each part randomly.\n   * @alias `const uid = new ShortUniqueId(); uid.rnd(uuidLength: number);`\n   */\n  randomUUID = (uuidLength: number = this.uuidLength || DEFAULT_UUID_LENGTH): string => {\n    if (uuidLength === null || typeof uuidLength === 'undefined' || uuidLength < 1) {\n      throw new Error('Invalid UUID Length Provided');\n    }\n\n    // Pre-allocate array for better performance with string concatenation\n    const result = new Array(uuidLength);\n    const dictLen = this.dictLength;\n    const dict = this.dict;\n\n    // Generate random ID parts from Dictionary\n    for (let j = 0; j < uuidLength; j++) {\n      // More efficient random index calculation - avoids string conversion and parsing\n      const randomPartIdx = Math.floor(Math.random() * dictLen);\n      result[j] = dict[randomPartIdx];\n    }\n\n    // Join array to string once at the end - more efficient than repeated concatenation\n    return result.join('');\n  };\n\n  fmt = (format: string, date?: Date): string => {\n    return this.formattedUUID(format, date);\n  };\n\n  /**\n   * Generates custom UUID with the provided format string.\n   * @alias `const uid = new ShortUniqueId(); uid.fmt(format: string);`\n   */\n  formattedUUID = (format: string, date?: Date): string => {\n    const fnMap = {\n      $r: this.randomUUID,\n      $s: this.sequentialUUID,\n      $t: this.stamp,\n    };\n\n    const result = format.replace(/\\$[rs]\\d{0,}|\\$t0|\\$t[1-9]\\d{1,}/g, (m) => {\n      const fn = m.slice(0, 2);\n      const len = Number.parseInt(m.slice(2), 10);\n\n      if (fn === '$s') {\n        return fnMap[fn]().padStart(len, '0');\n      }\n\n      if (fn === '$t' && date) {\n        return fnMap[fn](len, date);\n      }\n\n      return fnMap[fn as keyof typeof fnMap](len);\n    });\n\n    return result;\n  };\n\n  /**\n   * Calculates total number of possible UUIDs.\n   *\n   * Given that:\n   *\n   * - `H` is the total number of possible UUIDs\n   * - `n` is the number of unique characters in the dictionary\n   * - `l` is the UUID length\n   *\n   * Then `H` is defined as `n` to the power of `l`:\n   *\n   * <div style=\"background: white; padding: 5px; border-radius: 5px; overflow: hidden;\">\n   *  <img src=\"data:image/png;base64,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\" />\n   * </div>\n   *\n   * This function returns `H`.\n   */\n  availableUUIDs = (uuidLength: number = this.uuidLength): number => {\n    return Number.parseFloat(([...new Set(this.dict)].length ** uuidLength).toFixed(0));\n  };\n\n  /**\n   * Calculates approximate number of hashes before first collision.\n   *\n   * Given that:\n   *\n   * - `H` is the total number of possible UUIDs, or in terms of this library,\n   * the result of running `availableUUIDs()`\n   * - the expected number of values we have to choose before finding the\n   * first collision can be expressed as the quantity `Q(H)`\n   *\n   * Then `Q(H)` can be approximated as the square root of the product of half\n   * of pi times `H`:\n   *\n   * <div style=\"background: white; padding: 5px; border-radius: 5px; overflow: hidden;\">\n   *  <img src=\"data:image/png;base64,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\" />\n   * </div>\n   *\n   * This function returns `Q(H)`.\n   *\n   * (see [Poisson distribution](https://en.wikipedia.org/wiki/Poisson_distribution))\n   */\n  // Cache for memoization\n  private _collisionCache: Map<number, number> = new Map();\n\n  approxMaxBeforeCollision = (rounds: number = this.availableUUIDs(this.uuidLength)): number => {\n    // Check if result is cached\n    const cacheKey = rounds;\n    const cached = this._collisionCache.get(cacheKey);\n    if (cached !== undefined) {\n      return cached;\n    }\n\n    // Calculate and cache result\n    // Match the exact output of the original implementation\n    const result = Number.parseFloat(Math.sqrt((Math.PI / 2) * rounds).toFixed(20));\n    this._collisionCache.set(cacheKey, result);\n    return result;\n  };\n\n  /**\n   * Calculates probability of generating duplicate UUIDs (a collision) in a\n   * given number of UUID generation rounds.\n   *\n   * Given that:\n   *\n   * - `r` is the maximum number of times that `randomUUID()` will be called,\n   * or better said the number of _rounds_\n   * - `H` is the total number of possible UUIDs, or in terms of this library,\n   * the result of running `availableUUIDs()`\n   *\n   * Then the probability of collision `p(r; H)` can be approximated as the result\n   * of dividing the square root of the product of half of pi times `r` by `H`:\n   *\n   * <div style=\"background: white; padding: 5px; border-radius: 5px; overflow: hidden;\">\n   *  <img src=\"data:image/png;base64,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\" />\n   * </div>\n   *\n   * This function returns `p(r; H)`.\n   *\n   * (see [Poisson distribution](https://en.wikipedia.org/wiki/Poisson_distribution))\n   *\n   * (Useful if you are wondering _\"If I use this lib and expect to perform at most\n   * `r` rounds of UUID generations, what is the probability that I will hit a duplicate UUID?\"_.)\n   */\n  collisionProbability = (\n    rounds: number = this.availableUUIDs(this.uuidLength),\n    uuidLength: number = this.uuidLength,\n  ): number => {\n    return Number.parseFloat(\n      (this.approxMaxBeforeCollision(rounds) / this.availableUUIDs(uuidLength)).toFixed(20),\n    );\n  };\n\n  /**\n   * Calculate a \"uniqueness\" score (from 0 to 1) of UUIDs based on size of\n   * dictionary and chosen UUID length.\n   *\n   * Given that:\n   *\n   * - `H` is the total number of possible UUIDs, or in terms of this library,\n   * the result of running `availableUUIDs()`\n   * - `Q(H)` is the approximate number of hashes before first collision,\n   * or in terms of this library, the result of running `approxMaxBeforeCollision()`\n   *\n   * Then `uniqueness` can be expressed as the additive inverse of the probability of\n   * generating a \"word\" I had previously generated (a duplicate) at any given iteration\n   * up to the the total number of possible UUIDs expressed as the quotiend of `Q(H)` and `H`:\n   *\n   * <div style=\"background: white; padding: 5px; border-radius: 5px; overflow: hidden;\">\n   *  <img src=\"data:image/png;base64,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\" />\n   * </div>\n   *\n   * (Useful if you need a value to rate the \"quality\" of the combination of given dictionary\n   * and UUID length. The closer to 1, higher the uniqueness and thus better the quality.)\n   */\n  uniqueness = (rounds: number = this.availableUUIDs(this.uuidLength)): number => {\n    const score = Number.parseFloat(\n      (1 - this.approxMaxBeforeCollision(rounds) / rounds).toFixed(20),\n    );\n    return score > 1 ? 1 : score < 0 ? 0 : score;\n  };\n\n  /**\n   * Return the version of this module.\n   */\n  getVersion = (): string => {\n    return this.version;\n  };\n\n  /**\n   * Generates a UUID with a timestamp that can be extracted using `uid.parseStamp(stampString);`.\n   *\n   * ```js\n   *  const uidWithTimestamp = uid.stamp(32);\n   *  console.log(uidWithTimestamp);\n   *  // GDa608f973aRCHLXQYPTbKDbjDeVsSb3\n   *\n   *  console.log(uid.parseStamp(uidWithTimestamp));\n   *  // 2021-05-03T06:24:58.000Z\n   *  ```\n   */\n  stamp = (finalLength: number, date?: Date): string => {\n    const hexStamp = Math.floor(+(date || new Date()) / 1000).toString(16);\n\n    if (typeof finalLength === 'number' && finalLength === 0) {\n      return hexStamp;\n    }\n\n    if (typeof finalLength !== 'number' || finalLength < 10) {\n      throw new Error(\n        [\n          'Param finalLength must be a number greater than or equal to 10,',\n          'or 0 if you want the raw hexadecimal timestamp',\n        ].join('\\n'),\n      );\n    }\n\n    const idLength = finalLength - 9;\n\n    const rndIdx = Math.round(Math.random() * (idLength > 15 ? 15 : idLength));\n\n    const id = this.randomUUID(idLength);\n\n    return `${id.substring(0, rndIdx)}${hexStamp}${id.substring(rndIdx)}${rndIdx.toString(16)}`;\n  };\n\n  /**\n   * Extracts the date embeded in a UUID generated using the `uid.stamp(finalLength);` method.\n   *\n   * ```js\n   *  const uidWithTimestamp = uid.stamp(32);\n   *  console.log(uidWithTimestamp);\n   *  // GDa608f973aRCHLXQYPTbKDbjDeVsSb3\n   *\n   *  console.log(uid.parseStamp(uidWithTimestamp));\n   *  // 2021-05-03T06:24:58.000Z\n   *  ```\n   */\n  parseStamp = (suid: string, format?: string): Date => {\n    if (format && !/t0|t[1-9]\\d{1,}/.test(format)) {\n      throw new Error('Cannot extract date from a formated UUID with no timestamp in the format');\n    }\n\n    const stamp = format\n      ? format\n          .replace(/\\$[rs]\\d{0,}|\\$t0|\\$t[1-9]\\d{1,}/g, (m) => {\n            const fnMap = {\n              $r: (len: number) => [...Array(len)].map(() => 'r').join(''),\n              $s: (len: number) => [...Array(len)].map(() => 's').join(''),\n              $t: (len: number) => [...Array(len)].map(() => 't').join(''),\n            };\n\n            const fn = m.slice(0, 2);\n            const len = Number.parseInt(m.slice(2), 10);\n\n            return fnMap[fn as keyof typeof fnMap](len);\n          })\n          .replace(/^(.*?)(t{8,})(.*)$/g, (_m, p1, p2) => {\n            return suid.substring(p1.length, p1.length + p2.length);\n          })\n      : suid;\n\n    if (stamp.length === 8) {\n      return new Date(Number.parseInt(stamp, 16) * 1000);\n    }\n\n    if (stamp.length < 10) {\n      throw new Error('Stamp length invalid');\n    }\n\n    const rndIdx = Number.parseInt(stamp.substring(stamp.length - 1), 16);\n\n    return new Date(Number.parseInt(stamp.substring(rndIdx, rndIdx + 8), 16) * 1000);\n  };\n\n  /**\n   * Set the counter to a specific value.\n   */\n  setCounter = (counter: number): void => {\n    this.counter = counter;\n  };\n\n  /**\n   * Validate given UID contains only characters from the instanced dictionary or optionally provided dictionary.\n   */\n  validate = (uid: string, dictionary?: string[] | ShortUniqueIdDefaultDictionaries): boolean => {\n    const finalDictionary = dictionary ? this._normalizeDictionary(dictionary) : this.dict;\n\n    return uid.split('').every((c) => finalDictionary.includes(c));\n  };\n\n  constructor(argOptions: Partial<ShortUniqueIdOptions> = {}) {\n    const options: ShortUniqueIdOptions = {\n      ...DEFAULT_OPTIONS,\n      ...(argOptions as Partial<ShortUniqueIdOptions>),\n    };\n\n    this.counter = 0;\n    this.debug = false;\n    this.dict = [];\n    this.version = version;\n\n    const { dictionary, shuffle, length, counter } = options;\n\n    this.uuidLength = length;\n\n    this.setDictionary(dictionary, shuffle);\n    this.setCounter(counter);\n\n    this.debug = options.debug;\n    this.log(this.dict);\n    this.log(\n      `Generator instantiated with Dictionary Size ${this.dictLength} and counter set to ${this.counter}`,\n    );\n\n    this.log = this.log.bind(this);\n    this.setDictionary = this.setDictionary.bind(this);\n    this.setCounter = this.setCounter.bind(this);\n    this.seq = this.seq.bind(this);\n    this.sequentialUUID = this.sequentialUUID.bind(this);\n    this.rnd = this.rnd.bind(this);\n    this.randomUUID = this.randomUUID.bind(this);\n    this.fmt = this.fmt.bind(this);\n    this.formattedUUID = this.formattedUUID.bind(this);\n    this.availableUUIDs = this.availableUUIDs.bind(this);\n    this.approxMaxBeforeCollision = this.approxMaxBeforeCollision.bind(this);\n    this.collisionProbability = this.collisionProbability.bind(this);\n    this.uniqueness = this.uniqueness.bind(this);\n    this.getVersion = this.getVersion.bind(this);\n    this.stamp = this.stamp.bind(this);\n    this.parseStamp = this.parseStamp.bind(this);\n  }\n}\n", "{\n  \"name\": \"short-unique-id\",\n  \"version\": \"5.3.2\",\n  \"description\": \"Generate random or sequential UUID of any length\",\n  \"keywords\": [\n    \"short\",\n    \"random\",\n    \"uid\",\n    \"uuid\",\n    \"guid\",\n    \"node\",\n    \"unique id\",\n    \"generator\",\n    \"tiny\"\n  ],\n  \"bin\": {\n    \"short-unique-id\": \"bin/short-unique-id\",\n    \"suid\": \"bin/short-unique-id\"\n  },\n  \"main\": \"dist/short-unique-id.js\",\n  \"types\": \"dist/short-unique-id.d.ts\",\n  \"homepage\": \"https://shortunique.id\",\n  \"repository\": {\n    \"type\": \"git\",\n    \"url\": \"https://github.com/jeanlescure/short-unique-id\"\n  },\n  \"license\": \"Apache-2.0\",\n  \"runkitExampleFilename\": \"./runkit.js\",\n  \"scripts\": {\n    \"types:check\": \"tsc --noEmit\",\n    \"test\": \"tsx ./src/test.ts\",\n    \"test:all\": \"npm run build && tsc --noEmit --project ./specs/esm/tsconfig.json && tsx ./specs/esm/import.spec.ts && node ./specs/cjs/require.spec.js && npm run test\",\n    \"build\": \"./scripts/build && tsc --noEmit ./dist/short-unique-id.d.ts\",\n    \"docs\": \"./scripts/docs\",\n    \"release\": \"release-it\"\n  },\n  \"release-it\": {\n    \"git\": {\n      \"changelog\": \"auto-changelog --stdout -l false -u -t ./assets/changelog-compact.hbs\"\n    },\n    \"hooks\": {\n      \"after:bump\": \"./scripts/release\"\n    },\n    \"npm\": {\n      \"publish\": false\n    }\n  },\n  \"files\": [\n    \"bin\",\n    \"dist\",\n    \"runkit.js\",\n    \"package.json\"\n  ],\n  \"devDependencies\": {\n    \"@types/node\": \"^22.13.13\",\n    \"auto-changelog\": \"^2.5.0\",\n    \"esbuild\": \"^0.25.1\",\n    \"refup\": \"^1.1.0\",\n    \"release-it\": \"^18.1.2\",\n    \"tslib\": \"^2.8.1\",\n    \"tsx\": \"^4.19.3\",\n    \"typedoc\": \"^0.25.13\",\n    \"typedoc-plugin-extras\": \"^3.0.0\",\n    \"typedoc-plugin-rename-defaults\": \"^0.7.0\",\n    \"typedoc-plugin-script-inject\": \"^2.0.0\",\n    \"typescript\": \"^5.8.2\"\n  }\n}\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAAA,UAAA,gBAAA,CAAA;AAAA,eAAA,eAAA;QAAA,iBAAA,MAAA;QAAA,qBAAA,MAAA;QAAA,SAAA,MAAAA;MAAA,CAAA;ACEE,UAAA,UAAW;ADgEN,UAAM,sBAA8B;AAEpC,UAAM,kBAAwC;QACnD,YAAY;QACZ,SAAS;QACT,OAAO;QACP,QAAQ;QACR,SAAS;MACX;AA0DA,UAAqB,iBAArB,MAAqB,eAAc;QAsejC,YAAY,aAA4C,CAAC,GAAG;AAle5D,wBAAA,MAAO,SAAA;AACP,wBAAA,MAAO,OAAA;AACP,wBAAA,MAAO,MAAA;AACP,wBAAA,MAAO,SAAA;AACP,wBAAA,MAAO,aAAY,CAAA;AACnB,wBAAA,MAAO,aAAsB,CAAC,CAAA;AAC9B,wBAAA,MAAO,cAAa,CAAA;AACpB,wBAAA,MAAO,cAAa,CAAA;AACpB,wBAAA,MAAO,cAAa,CAAA;AACpB,wBAAA,MAAO,YAAA;AAEP,wBAAA,MAAU,sBAAqB,EAAA;AAC/B,wBAAA,MAAU,qBAAoB,EAAA;AAC9B,wBAAA,MAAU,4BAA2B,EAAA;AACrC,wBAAA,MAAU,2BAA0B,GAAA;AACpC,wBAAA,MAAU,mBAAkB,GAAA;AAC5B,wBAAA,MAAU,4BAA2B,EAAA;AACrC,wBAAA,MAAU,2BAA0B,EAAA;AAEpC,wBAAA,MAAU,uBAA2C;YACnD,QAAQ,CAAC,KAAK,oBAAoB,KAAK,iBAAiB;UAC1D,CAAA;AAEA,wBAAA,MAAU,sBAA0C;YAClD,WAAW,CAAC,KAAK,0BAA0B,KAAK,uBAAuB;YACvE,WAAW,CAAC,KAAK,0BAA0B,KAAK,uBAAuB;UACzE,CAAA;AAEA,wBAAA,MAAU,4BAAgD;YACxD,WAAW,CAAC,KAAK,0BAA0B,KAAK,uBAAuB;UACzE,CAAA;AAEA,wBAAA,MAAU,4BAAgD;YACxD,WAAW,CAAC,KAAK,0BAA0B,KAAK,uBAAuB;UACzE,CAAA;AAEA,wBAAA,MAAU,yBAA6C;YACrD,QAAQ,CAAC,KAAK,oBAAoB,KAAK,iBAAiB;YACxD,WAAW,CAAC,KAAK,0BAA0B,KAAK,uBAAuB;YACvE,WAAW,CAAC,KAAK,0BAA0B,KAAK,uBAAuB;UACzE,CAAA;AAEA,wBAAA,MAAU,+BAAmD;YAC3D,QAAQ,CAAC,KAAK,oBAAoB,KAAK,iBAAiB;YACxD,WAAW,CAAC,KAAK,0BAA0B,KAAK,uBAAuB;UACzE,CAAA;AAEA,wBAAA,MAAU,+BAAmD;YAC3D,QAAQ,CAAC,KAAK,oBAAoB,KAAK,iBAAiB;YACxD,WAAW,CAAC,KAAK,0BAA0B,KAAK,uBAAuB;UACzE,CAAA;AAEA,wBAAA,MAAU,oBAAwC;YAChD,WAAW,CAAC,KAAK,oBAAoB,KAAK,iBAAiB;YAC3D,aAAa,CAAC,KAAK,0BAA0B,KAAK,eAAe;UACnE,CAAA;AAEA,wBAAA,MAAU,gBAAuC;YAC/C,qBAAqB,KAAK;YAC1B,oBAAoB,KAAK;YACzB,0BAA0B,KAAK;YAC/B,0BAA0B,KAAK;YAC/B,uBAAuB,KAAK;YAC5B,6BAA6B,KAAK;YAClC,6BAA6B,KAAK;YAClC,kBAAkB,KAAK;UACzB,CAAA;AAGA,wBAAA,MAAU,OAAM,IAAI,SAA0B;AAC5C,kBAAM,YAAY,CAAC,GAAG,IAAI;AAC1B,sBAAU,CAAC,IAAI,qBAAqB,OAAA,KAAK,CAAC,CAAA;AAE1C,gBAAI,KAAK,UAAU,MAAM;AACvB,kBAAI,OAAO,YAAY,eAAe,YAAY,MAAM;AACtD,wBAAQ,IAAI,GAAG,SAAS;AACxB;cACF;YACF;UAEF,CAAA;AAGA,wBAAA,MAAU,wBAAuB,CAC/BC,aACAC,aACa;AACb,gBAAI;AAEJ,gBAAID,eAAc,MAAM,QAAQA,WAAU,KAAKA,YAAW,SAAS,GAAG;AACpE,0BAAYA;YACd,OAAO;AACL,0BAAY,CAAC;AACb,mBAAK,YAAY;AAEjB,oBAAM,aAAa,IAAI,OAAAA,aAA8C,cAAA;AACrE,oBAAM,SAAS,KAAK,aAAa,UAAU;AAG3C,kBAAI,WAAW;AACf,yBAAW,CAAC,EAAE,UAAU,KAAK,OAAO,QAAQ,MAAM,GAAG;AACnD,sBAAM,CAAC,OAAO,KAAK,IAAI;AACvB,4BAAY,KAAK,IAAI,QAAQ,KAAK;cACpC;AACA,0BAAY,IAAI,MAAM,QAAQ;AAE9B,kBAAI,UAAU;AACd,yBAAW,CAAC,EAAE,cAAc,KAAK,OAAO,QAAQ,MAAM,GAAG;AACvD,qBAAK,YAAY;AACjB,qBAAK,aAAa,KAAK,UAAU,CAAC;AAClC,qBAAK,aAAa,KAAK,UAAU,CAAC;AAClC,sBAAM,cAAc,KAAK,cAAc,KAAK;AAC5C,sBAAM,QAAQ,KAAK;AACnB,sBAAM,MAAM,KAAK;AAEjB,oBAAI,aAAa;AACf,2BAAS,IAAI,OAAO,IAAI,KAAK,KAAK;AAChC,8BAAU,SAAS,IAAI,OAAO,aAAa,CAAC;AAC5C,yBAAK,YAAY;kBACnB;gBACF,OAAO;AACL,2BAAS,IAAI,OAAO,IAAI,KAAK,KAAK;AAChC,8BAAU,SAAS,IAAI,OAAO,aAAa,CAAC;AAC5C,yBAAK,YAAY;kBACnB;gBACF;cACF;AACA,wBAAU,SAAS;YACrB;AAEA,gBAAIC,UAAS;AAEX,oBAAM,MAAM,UAAU;AACtB,uBAAS,IAAI,MAAM,GAAG,IAAI,GAAG,KAAK;AAChC,sBAAM,IAAI,KAAK,MAAM,KAAK,OAAO,KAAK,IAAI,EAAE;AAC5C,iBAAC,UAAU,CAAC,GAAG,UAAU,CAAC,CAAC,IAAI,CAAC,UAAU,CAAC,GAAG,UAAU,CAAC,CAAC;cAC5D;YACF;AAEA,mBAAO;UACT,CAAA;AAGA,wBAAA,MAAA,iBAAgB,CACdD,aACAC,aACS;AACT,iBAAK,OAAO,KAAK,qBAAqBD,aAAYC,QAAO;AAGzD,iBAAK,aAAa,KAAK,KAAK;AAG5B,iBAAK,WAAW,CAAC;UACnB,CAAA;AAEA,wBAAA,MAAA,OAAM,MAAc;AAClB,mBAAO,KAAK,eAAe;UAC7B,CAAA;AAMA,wBAAA,MAAA,kBAAiB,MAAc;AAE7B,kBAAM,UAAU,KAAK;AACrB,kBAAM,OAAO,KAAK;AAClB,gBAAI,aAAa,KAAK;AAGtB,kBAAM,UAAU,CAAC;AAGjB,eAAG;AACD,oBAAM,aAAa,aAAa;AAChC,2BAAa,KAAK,MAAM,aAAa,OAAO;AAC5C,sBAAQ,KAAK,KAAK,UAAU,CAAC;YAC/B,SAAS,eAAe;AAGxB,kBAAM,KAAK,QAAQ,KAAK,EAAE;AAE1B,iBAAK,WAAW;AAEhB,mBAAO;UACT,CAAA;AAEA,wBAAA,MAAA,OAAM,CAAC,aAAqB,KAAK,cAAc,wBAAgC;AAC7E,mBAAO,KAAK,WAAW,UAAU;UACnC,CAAA;AAMA,wBAAA,MAAA,cAAa,CAAC,aAAqB,KAAK,cAAc,wBAAgC;AACpF,gBAAI,eAAe,QAAQ,OAAO,eAAe,eAAe,aAAa,GAAG;AAC9E,oBAAM,IAAI,MAAM,8BAA8B;YAChD;AAGA,kBAAM,SAAS,IAAI,MAAM,UAAU;AACnC,kBAAM,UAAU,KAAK;AACrB,kBAAM,OAAO,KAAK;AAGlB,qBAAS,IAAI,GAAG,IAAI,YAAY,KAAK;AAEnC,oBAAM,gBAAgB,KAAK,MAAM,KAAK,OAAO,IAAI,OAAO;AACxD,qBAAO,CAAC,IAAI,KAAK,aAAa;YAChC;AAGA,mBAAO,OAAO,KAAK,EAAE;UACvB,CAAA;AAEA,wBAAA,MAAA,OAAM,CAAC,QAAgB,SAAwB;AAC7C,mBAAO,KAAK,cAAc,QAAQ,IAAI;UACxC,CAAA;AAMA,wBAAA,MAAA,iBAAgB,CAAC,QAAgB,SAAwB;AACvD,kBAAM,QAAQ;cACZ,IAAI,KAAK;cACT,IAAI,KAAK;cACT,IAAI,KAAK;YACX;AAEA,kBAAM,SAAS,OAAO,QAAQ,qCAAqC,CAAC,MAAM;AACxE,oBAAM,KAAK,EAAE,MAAM,GAAG,CAAC;AACvB,oBAAM,MAAM,OAAO,SAAS,EAAE,MAAM,CAAC,GAAG,EAAE;AAE1C,kBAAI,OAAO,MAAM;AACf,uBAAO,MAAM,EAAE,EAAE,EAAE,SAAS,KAAK,GAAG;cACtC;AAEA,kBAAI,OAAO,QAAQ,MAAM;AACvB,uBAAO,MAAM,EAAE,EAAE,KAAK,IAAI;cAC5B;AAEA,qBAAO,MAAM,EAAwB,EAAE,GAAG;YAC5C,CAAC;AAED,mBAAO;UACT,CAAA;AAmBA,wBAAA,MAAA,kBAAiB,CAAC,aAAqB,KAAK,eAAuB;AACjE,mBAAO,OAAO,YAAY,CAAC,GAAG,IAAI,IAAI,KAAK,IAAI,CAAC,EAAE,UAAU,YAAY,QAAQ,CAAC,CAAC;UACpF,CAAA;AAwBA,wBAAA,MAAQ,mBAAuC,oBAAI,IAAI,CAAA;AAEvD,wBAAA,MAAA,4BAA2B,CAAC,SAAiB,KAAK,eAAe,KAAK,UAAU,MAAc;AAE5F,kBAAM,WAAW;AACjB,kBAAM,SAAS,KAAK,gBAAgB,IAAI,QAAQ;AAChD,gBAAI,WAAW,QAAW;AACxB,qBAAO;YACT;AAIA,kBAAM,SAAS,OAAO,WAAW,KAAK,KAAM,KAAK,KAAK,IAAK,MAAM,EAAE,QAAQ,EAAE,CAAC;AAC9E,iBAAK,gBAAgB,IAAI,UAAU,MAAM;AACzC,mBAAO;UACT,CAAA;AA2BA,wBAAA,MAAA,wBAAuB,CACrB,SAAiB,KAAK,eAAe,KAAK,UAAU,GACpD,aAAqB,KAAK,eACf;AACX,mBAAO,OAAO;eACX,KAAK,yBAAyB,MAAM,IAAI,KAAK,eAAe,UAAU,GAAG,QAAQ,EAAE;YACtF;UACF,CAAA;AAwBA,wBAAA,MAAA,cAAa,CAAC,SAAiB,KAAK,eAAe,KAAK,UAAU,MAAc;AAC9E,kBAAM,QAAQ,OAAO;eAClB,IAAI,KAAK,yBAAyB,MAAM,IAAI,QAAQ,QAAQ,EAAE;YACjE;AACA,mBAAO,QAAQ,IAAI,IAAI,QAAQ,IAAI,IAAI;UACzC,CAAA;AAKA,wBAAA,MAAA,cAAa,MAAc;AACzB,mBAAO,KAAK;UACd,CAAA;AAcA,wBAAA,MAAA,SAAQ,CAAC,aAAqB,SAAwB;AACpD,kBAAM,WAAW,KAAK,MAAM,EAAE,QAAQ,oBAAI,KAAK,KAAK,GAAI,EAAE,SAAS,EAAE;AAErE,gBAAI,OAAO,gBAAgB,YAAY,gBAAgB,GAAG;AACxD,qBAAO;YACT;AAEA,gBAAI,OAAO,gBAAgB,YAAY,cAAc,IAAI;AACvD,oBAAM,IAAI;gBACR;kBACE;kBACA;gBACF,EAAE,KAAK,IAAI;cACb;YACF;AAEA,kBAAM,WAAW,cAAc;AAE/B,kBAAM,SAAS,KAAK,MAAM,KAAK,OAAO,KAAK,WAAW,KAAK,KAAK,SAAS;AAEzE,kBAAM,KAAK,KAAK,WAAW,QAAQ;AAEnC,mBAAO,GAAG,OAAA,GAAG,UAAU,GAAG,MAAM,CAAA,EAAI,OAAA,QAAA,EAAW,OAAA,GAAG,UAAU,MAAM,CAAA,EAAI,OAAA,OAAO,SAAS,EAAE,CAAA;UAC1F,CAAA;AAcA,wBAAA,MAAA,cAAa,CAAC,MAAc,WAA0B;AACpD,gBAAI,UAAU,CAAC,kBAAkB,KAAK,MAAM,GAAG;AAC7C,oBAAM,IAAI,MAAM,0EAA0E;YAC5F;AAEA,kBAAM,QAAQ,SACV,OACG,QAAQ,qCAAqC,CAAC,MAAM;AACnD,oBAAM,QAAQ;gBACZ,IAAI,CAACC,SAAgB,CAAC,GAAG,MAAMA,IAAG,CAAC,EAAE,IAAI,MAAM,GAAG,EAAE,KAAK,EAAE;gBAC3D,IAAI,CAACA,SAAgB,CAAC,GAAG,MAAMA,IAAG,CAAC,EAAE,IAAI,MAAM,GAAG,EAAE,KAAK,EAAE;gBAC3D,IAAI,CAACA,SAAgB,CAAC,GAAG,MAAMA,IAAG,CAAC,EAAE,IAAI,MAAM,GAAG,EAAE,KAAK,EAAE;cAC7D;AAEA,oBAAM,KAAK,EAAE,MAAM,GAAG,CAAC;AACvB,oBAAM,MAAM,OAAO,SAAS,EAAE,MAAM,CAAC,GAAG,EAAE;AAE1C,qBAAO,MAAM,EAAwB,EAAE,GAAG;YAC5C,CAAC,EACA,QAAQ,uBAAuB,CAAC,IAAI,IAAI,OAAO;AAC9C,qBAAO,KAAK,UAAU,GAAG,QAAQ,GAAG,SAAS,GAAG,MAAM;YACxD,CAAC,IACH;AAEJ,gBAAI,MAAM,WAAW,GAAG;AACtB,qBAAO,IAAI,KAAK,OAAO,SAAS,OAAO,EAAE,IAAI,GAAI;YACnD;AAEA,gBAAI,MAAM,SAAS,IAAI;AACrB,oBAAM,IAAI,MAAM,sBAAsB;YACxC;AAEA,kBAAM,SAAS,OAAO,SAAS,MAAM,UAAU,MAAM,SAAS,CAAC,GAAG,EAAE;AAEpE,mBAAO,IAAI,KAAK,OAAO,SAAS,MAAM,UAAU,QAAQ,SAAS,CAAC,GAAG,EAAE,IAAI,GAAI;UACjF,CAAA;AAKA,wBAAA,MAAA,cAAa,CAACC,aAA0B;AACtC,iBAAK,UAAUA;UACjB,CAAA;AAKA,wBAAA,MAAA,YAAW,CAAC,KAAaH,gBAAsE;AAC7F,kBAAM,kBAAkBA,cAAa,KAAK,qBAAqBA,WAAU,IAAI,KAAK;AAElF,mBAAO,IAAI,MAAM,EAAE,EAAE,MAAM,CAAC,MAAM,gBAAgB,SAAS,CAAC,CAAC;UAC/D,CAAA;AAGE,gBAAM,UAAgC,eAAA,eAAA,CAAA,GACjC,eAAA,GACC,UAAA;AAGN,eAAK,UAAU;AACf,eAAK,QAAQ;AACb,eAAK,OAAO,CAAC;AACb,eAAK,UAAU;AAEf,gBAAM,EAAE,YAAY,SAAS,QAAQ,QAAQ,IAAI;AAEjD,eAAK,aAAa;AAElB,eAAK,cAAc,YAAY,OAAO;AACtC,eAAK,WAAW,OAAO;AAEvB,eAAK,QAAQ,QAAQ;AACrB,eAAK,IAAI,KAAK,IAAI;AAClB,eAAK;YACH,+CAA+C,OAAA,KAAK,YAAU,sBAAA,EAAuB,OAAA,KAAK,OAAA;UAC5F;AAEA,eAAK,MAAM,KAAK,IAAI,KAAK,IAAI;AAC7B,eAAK,gBAAgB,KAAK,cAAc,KAAK,IAAI;AACjD,eAAK,aAAa,KAAK,WAAW,KAAK,IAAI;AAC3C,eAAK,MAAM,KAAK,IAAI,KAAK,IAAI;AAC7B,eAAK,iBAAiB,KAAK,eAAe,KAAK,IAAI;AACnD,eAAK,MAAM,KAAK,IAAI,KAAK,IAAI;AAC7B,eAAK,aAAa,KAAK,WAAW,KAAK,IAAI;AAC3C,eAAK,MAAM,KAAK,IAAI,KAAK,IAAI;AAC7B,eAAK,gBAAgB,KAAK,cAAc,KAAK,IAAI;AACjD,eAAK,iBAAiB,KAAK,eAAe,KAAK,IAAI;AACnD,eAAK,2BAA2B,KAAK,yBAAyB,KAAK,IAAI;AACvE,eAAK,uBAAuB,KAAK,qBAAqB,KAAK,IAAI;AAC/D,eAAK,aAAa,KAAK,WAAW,KAAK,IAAI;AAC3C,eAAK,aAAa,KAAK,WAAW,KAAK,IAAI;AAC3C,eAAK,QAAQ,KAAK,MAAM,KAAK,IAAI;AACjC,eAAK,aAAa,KAAK,WAAW,KAAK,IAAI;QAC7C;MACF;AA7gBE,oBAFmB,gBAEZ,WAAgC,cAAA;AAFzC,UAAqBD,iBAArB;;;;;;", "names": ["ShortUniqueId", "dictionary", "shuffle", "len", "counter"]}